{"version": 3, "sources": ["../../phaser3-rex-plugins/templates/ui/ui-components.js"], "sourcesContent": ["import NinePatch from './ninepatch/NinePatch.js';\r\nimport NinePatch2 from './ninepatch2/NinePatch.js';\r\nimport RoundRectangle from './roundrectangle/RoundRectangle.js';\r\nimport RoundRectangleCanvas from './roundrectanglecanvas/RoundRectangleCanvas.js';\r\nimport QuadShape from './quadshape/QuadShape.js';\r\nimport BBCodeText from './bbcodetext/BBCodeText.js';\r\nimport TagText from './tagtext/TagText.js';\r\nimport DynamicText from './dynamictext/DynamicText.js';\r\nimport TextPlayer from './textplayer/TextPlayer.js';\r\nimport CanvasInput from './canvasinput/CanvasInput.js';\r\nimport HiddenEdit from './hiddenedit/HiddenEdit.js';\r\nimport Checkbox from './checkbox/Checkbox.js';\r\nimport ToggleSwitch from './toggleswitch/ToggleSwitch.js';\r\n\r\nimport Canvas from './canvas/Canvas.js';\r\nimport CircleMaskImage from './circlemaskimage/CircleMaskImage.js';\r\nimport AlphaMaskImage from './alphamaskimage/AlphaMaskImage.js';\r\nimport CircularProgress from './circularprogress/CircularProgress.js';\r\nimport CircularProgressCanvas from './circularprogresscanvas/CircularProgressCanvas.js';\r\nimport LineProgress from './lineprogress/LineProgress.js';\r\nimport RoundRectangleProgress from './roundrectangleprogress/RoundRectangleProgress.js';\r\nimport LineProgressCanvas from './lineprogresscanvas/LineProgressCanvas.js';\r\nimport Tirangle from './triangle/Triangle.js';\r\nimport Knob from './knob/Knob.js';\r\nimport CustomShapes from './customshapes/CustomShapes.js';\r\nimport CustomProgress from './customprogress/CustomProgress.js';\r\nimport AIOSpinner from './aiospinner/AIOSpinner.js';\r\nimport TransitionImage from './transitionimage/TransitionImage.js';\r\nimport TransitionImagePack from './transitionimagepack/TransitionImagePack.js';\r\nimport ImageBox from './imagebox/ImageBox.js';\r\nimport ImageInputLabel from './imageinputlabel/ImageInputLabel.js';\r\nimport FullWindowRectangle from './fullwindowrectangle/FullWindowRectangle.js';\r\nimport FullWindowZone from './fullwindowzone/FullWindowZone.js';\r\nimport Cover from './cover/Cover.js';\r\nimport InputText from './inputtext/InputText.js';\r\nimport { FileChooser } from './filechooser/FileChooser.js';\r\nimport FileDropZone from './filedropzone/FileDropZone.js';\r\nimport StatesBarRectangle from './statesbarrectangle/StatesBarRectangle.js';\r\nimport StatesBitmapText from './statesbitmaptext/StatesBitmapText.js';\r\nimport StatesImage from './statesimage/StatesImage.js';\r\nimport StatesNinePatch from './statesninepatch/StatesNinePatch.js';\r\nimport StatesNineSlice from './statesnineslice/StatesNineSlice.js';\r\nimport StatesRoundRectangle from './statesroundrectangle/StatesRoundRectangle.js';\r\nimport StatesText from './statestext/StatesText.js';\r\nimport Chart from './chart/Chart.js';\r\n\r\nimport Container from './container/Container.js';\r\nimport Sizer from './sizer/Sizer.js';\r\nimport GridSizer from './gridsizer/GridSizer.js';\r\nimport FixWidthSizer from './fixwidthsizer/FixWidthSizer.js';\r\nimport OverlapSizer from './overlapsizer/OverlapSizer.js';\r\n\r\nimport Space from './space/Space.js';\r\nimport Label from './label/Label.js';\r\nimport SimpleLabel from './simplelabel/SimpleLabel.js';\r\nimport TitleLabel from './titlelabel/TitleLabel.js';\r\nimport SimpleTitleLabel from './simpletitlelabel/SimpleTitleLabel.js';\r\nimport NameValueLabel from './namevaluelabel/NameValueLabel.js';\r\nimport ExpBar from './expbar/ExpBar.js';\r\nimport Buttons from './buttons/Buttons.js';\r\nimport GridButtons from './gridbuttons/GridButtons.js';\r\nimport FixWidthButtons from './fixwidthbuttons/FixWidthButtons.js';\r\nimport FileSelectorButton from './fileselectorbutton/FileSelectorButton.js';\r\nimport Dialog from './dialog/Dialog.js';\r\nimport ConfirmDialog from './confirmdialog/ConfirmDialog.js';\r\nimport ConfirmActionButton from './confirmactionbutton/ConfirmActionButton.js';\r\nimport NameInputDialog from './nameinputdialog/NameInputDialog.js';\r\nimport HolyGrail from './holygrail/HolyGrail.js';\r\nimport Tabs from './tabs/Tabs.js';\r\nimport Slider from './slider/Slider.js';\r\nimport GridTable from './gridtable/GridTable.js';\r\nimport Menu from './menu/Menu.js';\r\nimport DropDownList from './dropdownlist/DropDownList.js';\r\nimport SimpleDropDownList from './simpledropdownlist/SimpleDropDownList.js';\r\nimport TextBox from './textbox/TextBox.js';\r\nimport SimpleTextBox from './simpletextbox/SimpleTextBox.js';\r\nimport NumberBar from './numberbar/NumberBar.js';\r\nimport BadgeLabel from './badgelabel/BadgeLabel.js';\r\nimport Pages from './pages/Pages.js';\r\nimport PerspectiveCard from './perspectivecard/PerspectiveCard.js';\r\nimport TabPages from './tabpages/TabPages.js';\r\nimport Folder from './folder/Folder.js';\r\nimport Trees from './trees/Trees.js';\r\nimport TextArea from './textarea/TextArea.js';\r\nimport TextAreaInput from './textareainput/TextAreaInput.js';\r\nimport ScrollablePanel from './scrollablepanel/ScrollablePanel.js';\r\nimport ScrollBar from './scrollbar/ScrollBar.js';\r\nimport Toast from './toast/Toast.js';\r\nimport ToastQueue from './toastqueue/ToastQueue.js';\r\nimport ColorComponents from './colorinput/colorcomponents/ColorComponents.js';\r\nimport ColorInput from './colorinput/colorinput/ColorInput.js';\r\nimport ColorInputBase from './colorinput/colorinputbase/ColorInputBase.js';\r\nimport ColorPicker from './colorinput/colorpicker/ColorPicker.js';\r\nimport SplitPanels from './splitpanels/SplitPanels.js';\r\nimport Tweaker from './tweaker/Tweaker.js';\r\nimport Sides from './sides/Sides.js';\r\n\r\nimport Click from './click/Click.js';\r\nimport ClickOutside from './clickoutside/ClickOutside.js';\r\nimport InTouching from './intouching/InTouching.js';\r\nimport Tap from './tap/Tap.js';\r\nimport Press from './press/Press.js';\r\nimport Swipe from './swipe/Swipe.js';\r\nimport Pan from './pan/Pan.js';\r\nimport Drag from './drag/Drag.js';\r\nimport Pinch from './pinch/Pinch.js';\r\nimport Rotate from './rotate/Rotate.js';\r\nimport Flip from './flip/Flip.js';\r\nimport Shake from './shake/Shake.js';\r\nimport TouchEventStop from './toucheventstop/TouchEventStop.js';\r\nimport Perspective from './perspective/Perspective.js';\r\nimport Skew from './skew/Skew.js';\r\nimport Anchor from './anchor/Anchor.js';\r\nimport TextTyping from './texttyping/TextTyping.js';\r\nimport TextPage from './textpage/TextPage.js';\r\nimport TextEdit from './textedit/TextEdit.js';\r\nimport { Fade, FadeIn, FadeOutDestroy } from './fade/Fade.js';\r\nimport { EaseMove, EaseMoveTo, EaseMoveFrom } from './easemove/EaseMove.js';\r\nimport { Modal, ModalPromise, ModalClose } from './modal/Modal.js';\r\n\r\nimport { GetParentSizer, GetTopmostSizer } from './utils/GetParentSizer.js';\r\nimport IsPointerInBounds from '../../plugins/utils/input/IsPointerInBounds.js';\r\nimport {\r\n    Show,\r\n    Hide,\r\n    IsShown,\r\n} from './utils/Hide.js';\r\nimport ConfirmAction from './confirmdialog/ConfirmAction.js';\r\nimport Edit from './textedit/Edit.js';\r\nimport WrapExpandText from './utils/wrapexpandtext/WrapExpandText.js';\r\nimport FontSizeExpandText from './utils/fontsizeexpandtext/FontSizeExpandText.js';\r\nimport SetFontSizeToFitWidth from '../../plugins/utils/text/fontsizefit/FontSizeFit.js';\r\nimport { WaitEvent, WaitComplete } from './utils/WaitEvent.js';\r\nimport DelayPromise from '../../plugins/utils/promise/Delay.js'\r\nimport GetViewport from '../../plugins/utils/system/GetViewport.js';\r\nimport SetChildrenInteractive from './utils/setchildreninteractive/SetChildrenInteractive.js';\r\nimport RequestDrag from '../../plugins/utils/input/RequestDrag.js';\r\nimport { OpenFileChooser } from './filechooser/FileChooser.js';\r\nimport LayerManager from './layermanager/LayerManager.js';\r\n\r\nconst FontSizeResize = SetFontSizeToFitWidth;\r\n\r\nexport {\r\n    NinePatch,\r\n    NinePatch2,\r\n    RoundRectangle,\r\n    RoundRectangleCanvas,\r\n    QuadShape,\r\n    BBCodeText,\r\n    TagText,\r\n    DynamicText,\r\n    TextPlayer,\r\n    CanvasInput,\r\n    HiddenEdit,\r\n    Checkbox,\r\n    ToggleSwitch,\r\n    Canvas,\r\n    CircleMaskImage,\r\n    AlphaMaskImage,\r\n    FullWindowRectangle,\r\n    FullWindowZone,\r\n    Cover,\r\n    InputText,\r\n    FileChooser,\r\n    FileDropZone,\r\n    StatesBarRectangle,\r\n    StatesBitmapText,\r\n    StatesImage,\r\n    StatesNinePatch,\r\n    StatesNineSlice,\r\n    StatesRoundRectangle,\r\n    StatesText,\r\n    Chart,\r\n    CircularProgress,\r\n    CircularProgressCanvas,\r\n    LineProgress,\r\n    RoundRectangleProgress,\r\n    LineProgressCanvas,\r\n    Tirangle,\r\n    Knob,\r\n    CustomShapes,\r\n    CustomProgress,\r\n    AIOSpinner,\r\n    TransitionImage,\r\n    TransitionImagePack,\r\n    ImageBox,\r\n    ImageInputLabel,\r\n\r\n    Container,\r\n    Sizer,\r\n    GridSizer,\r\n    FixWidthSizer,\r\n    OverlapSizer,\r\n\r\n    Space,\r\n    Label,\r\n    SimpleLabel,\r\n    TitleLabel,\r\n    SimpleTitleLabel,\r\n    NameValueLabel,\r\n    ExpBar,\r\n    Buttons,\r\n    GridButtons,\r\n    FixWidthButtons,\r\n    FileSelectorButton,\r\n    Dialog,\r\n    ConfirmDialog,\r\n    ConfirmActionButton,\r\n    NameInputDialog,\r\n    HolyGrail,\r\n    Tabs,\r\n    Slider,\r\n    GridTable,\r\n    Menu,\r\n    DropDownList,\r\n    SimpleDropDownList,\r\n    TextBox,\r\n    SimpleTextBox,\r\n    NumberBar,\r\n    BadgeLabel,\r\n    Pages,\r\n    PerspectiveCard,\r\n    TabPages,\r\n    Folder,\r\n    Trees,\r\n    TextArea,\r\n    TextAreaInput,\r\n    ScrollablePanel,\r\n    ScrollBar,\r\n    Toast,\r\n    ToastQueue,\r\n    ColorComponents,\r\n    ColorInput,\r\n    ColorInputBase,\r\n    ColorPicker,\r\n    SplitPanels,\r\n    Tweaker,\r\n    Sides,\r\n\r\n    Click,\r\n    ClickOutside,\r\n    InTouching,\r\n    Tap,\r\n    Press,\r\n    Swipe,\r\n    Pan,\r\n    Drag,\r\n    Pinch,\r\n    Rotate,\r\n    Flip,\r\n    Shake,\r\n    TouchEventStop,\r\n    Perspective,\r\n    Skew,\r\n    Anchor,\r\n    TextTyping,\r\n    TextPage,\r\n    TextEdit,\r\n    Fade, FadeIn, FadeOutDestroy,\r\n    EaseMove, EaseMoveTo, EaseMoveFrom,\r\n    Modal, ModalPromise, ModalClose,\r\n\r\n    GetParentSizer,\r\n    GetTopmostSizer,\r\n    IsPointerInBounds,\r\n    Show,\r\n    Hide,\r\n    IsShown,\r\n    ConfirmAction,\r\n    Edit,\r\n    WrapExpandText,\r\n    FontSizeExpandText,\r\n    FontSizeResize,  // Backward compatibility\r\n    SetFontSizeToFitWidth,\r\n    WaitEvent,\r\n    WaitComplete,\r\n    DelayPromise,\r\n    GetViewport,\r\n    SetChildrenInteractive,\r\n    RequestDrag,\r\n    OpenFileChooser,\r\n    LayerManager,\r\n}"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4IA,IAAM,iBAAiB;", "names": []}