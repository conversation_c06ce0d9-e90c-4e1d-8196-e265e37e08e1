import {
  <PERSON><PERSON>pinner_default,
  AlphaMask<PERSON>mage_default,
  Anchor_default,
  BBCodeText_default,
  BadgeLabel_default,
  Buttons_default,
  CanvasInput_default,
  Canvas_default,
  Chart_default,
  Checkbox_default,
  CircleMaskImage_default,
  CircularProgressCanvas_default,
  CircularProgress_default,
  ClickOutside_default,
  Click_default,
  ColorComponents_default,
  ColorInputBase_default,
  ColorInput_default,
  ColorPicker_default,
  ConfirmActionButton_default,
  ConfirmAction_default,
  ConfirmDialog_default,
  Container_default,
  Cover_default,
  CustomProgress_default,
  CustomShapes_default,
  Delay_default,
  Dialog_default,
  Drag_default,
  DropDownList_default,
  DynamicText_default,
  EaseMoveFrom_default,
  EaseMoveTo_default,
  EaseMove_default,
  Edit_default,
  ExpBar_default,
  FileChooser_default,
  FileDropZone_default,
  FileSelectorButton_default,
  FixWidthButtons_default,
  FixWidthSizer_default,
  Flip_default,
  Folder_default,
  FontSizeExpandText_default,
  FontSizeFit_default,
  FullWindowRectangle_default,
  FullWindowZone_default,
  GetParent,
  GetTopmostParent,
  GetViewport_default,
  GridButtons_default,
  GridSizer_default,
  GridTable_default,
  HiddenEdit_default,
  Hide,
  HolyGrail_default,
  ImageBox_default,
  ImageInputLabel_default,
  InTouching_default,
  InputText_default,
  IsPointerInBounds_default,
  IsShown,
  Knob_default,
  Label_default,
  LayerManager_default,
  LineProgressCanvas_default,
  LineProgress_default,
  Menu_default,
  Modal,
  ModalClose,
  ModalPromise,
  NameInputDialog_default,
  NameValueLabel_default,
  NinePatch_default,
  NinePatch_default2,
  NumberBar_default,
  Open_default,
  OverlapSizer_default,
  Pages_default,
  Pan_default,
  PerspectiveCard_default,
  Perspective_default,
  Pinch_default,
  Press_default,
  QuadShape_default,
  RequestDrag_default,
  Rotate_default,
  RoundRectangleCanvas_default,
  RoundRectangleProgress_default,
  RoundRectangle_default,
  ScrollBar_default,
  ScrollablePanel_default,
  SetChildrenInteractive_default,
  Shake_default,
  Show,
  Sides_default,
  SimpleDropDownList_default,
  SimpleLabel_default,
  SimpleTextBox_default,
  SimpleTitleLabel_default,
  Sizer_default,
  Skew_default,
  Slider_default,
  Space_default,
  SplitPanels_default,
  StatesBarRectangle_default,
  StatesBitmapText_default,
  StatesImage_default,
  StatesNinePatch_default,
  StatesNineSlice_default,
  StatesRoundRectangle_default,
  StatesText_default,
  Swipe_default,
  TabPages_default,
  Tabs_default,
  TagText_default,
  Tap_default,
  TextAreaInput_default,
  TextArea_default,
  TextBox_default,
  TextEdit_default,
  TextPage_default,
  TextPlayer_default,
  TextTyping_default,
  TitleLabel_default,
  ToastQueue_default,
  Toast_default,
  ToggleSwitch_default,
  TouchEventStop_default,
  TransitionImagePack_default,
  TransitionImage_default,
  Trees_default,
  Triangle_default,
  Tweaker_default,
  WaitComplete,
  WaitEvent,
  WrapExpandText_default,
  fade_default,
  fade_in_default,
  fade_out_destroy_default
} from "./chunk-KH3HNOE5.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/phaser3-rex-plugins/templates/ui/ui-components.js
var FontSizeResize = FontSizeFit_default;
export {
  AIOSpinner_default as AIOSpinner,
  AlphaMaskImage_default as AlphaMaskImage,
  Anchor_default as Anchor,
  BBCodeText_default as BBCodeText,
  BadgeLabel_default as BadgeLabel,
  Buttons_default as Buttons,
  Canvas_default as Canvas,
  CanvasInput_default as CanvasInput,
  Chart_default as Chart,
  Checkbox_default as Checkbox,
  CircleMaskImage_default as CircleMaskImage,
  CircularProgress_default as CircularProgress,
  CircularProgressCanvas_default as CircularProgressCanvas,
  Click_default as Click,
  ClickOutside_default as ClickOutside,
  ColorComponents_default as ColorComponents,
  ColorInput_default as ColorInput,
  ColorInputBase_default as ColorInputBase,
  ColorPicker_default as ColorPicker,
  ConfirmAction_default as ConfirmAction,
  ConfirmActionButton_default as ConfirmActionButton,
  ConfirmDialog_default as ConfirmDialog,
  Container_default as Container,
  Cover_default as Cover,
  CustomProgress_default as CustomProgress,
  CustomShapes_default as CustomShapes,
  Delay_default as DelayPromise,
  Dialog_default as Dialog,
  Drag_default as Drag,
  DropDownList_default as DropDownList,
  DynamicText_default as DynamicText,
  EaseMove_default as EaseMove,
  EaseMoveFrom_default as EaseMoveFrom,
  EaseMoveTo_default as EaseMoveTo,
  Edit_default as Edit,
  ExpBar_default as ExpBar,
  fade_default as Fade,
  fade_in_default as FadeIn,
  fade_out_destroy_default as FadeOutDestroy,
  FileChooser_default as FileChooser,
  FileDropZone_default as FileDropZone,
  FileSelectorButton_default as FileSelectorButton,
  FixWidthButtons_default as FixWidthButtons,
  FixWidthSizer_default as FixWidthSizer,
  Flip_default as Flip,
  Folder_default as Folder,
  FontSizeExpandText_default as FontSizeExpandText,
  FontSizeResize,
  FullWindowRectangle_default as FullWindowRectangle,
  FullWindowZone_default as FullWindowZone,
  GetParent as GetParentSizer,
  GetTopmostParent as GetTopmostSizer,
  GetViewport_default as GetViewport,
  GridButtons_default as GridButtons,
  GridSizer_default as GridSizer,
  GridTable_default as GridTable,
  HiddenEdit_default as HiddenEdit,
  Hide,
  HolyGrail_default as HolyGrail,
  ImageBox_default as ImageBox,
  ImageInputLabel_default as ImageInputLabel,
  InTouching_default as InTouching,
  InputText_default as InputText,
  IsPointerInBounds_default as IsPointerInBounds,
  IsShown,
  Knob_default as Knob,
  Label_default as Label,
  LayerManager_default as LayerManager,
  LineProgress_default as LineProgress,
  LineProgressCanvas_default as LineProgressCanvas,
  Menu_default as Menu,
  Modal,
  ModalClose,
  ModalPromise,
  NameInputDialog_default as NameInputDialog,
  NameValueLabel_default as NameValueLabel,
  NinePatch_default as NinePatch,
  NinePatch_default2 as NinePatch2,
  NumberBar_default as NumberBar,
  Open_default as OpenFileChooser,
  OverlapSizer_default as OverlapSizer,
  Pages_default as Pages,
  Pan_default as Pan,
  Perspective_default as Perspective,
  PerspectiveCard_default as PerspectiveCard,
  Pinch_default as Pinch,
  Press_default as Press,
  QuadShape_default as QuadShape,
  RequestDrag_default as RequestDrag,
  Rotate_default as Rotate,
  RoundRectangle_default as RoundRectangle,
  RoundRectangleCanvas_default as RoundRectangleCanvas,
  RoundRectangleProgress_default as RoundRectangleProgress,
  ScrollBar_default as ScrollBar,
  ScrollablePanel_default as ScrollablePanel,
  SetChildrenInteractive_default as SetChildrenInteractive,
  FontSizeFit_default as SetFontSizeToFitWidth,
  Shake_default as Shake,
  Show,
  Sides_default as Sides,
  SimpleDropDownList_default as SimpleDropDownList,
  SimpleLabel_default as SimpleLabel,
  SimpleTextBox_default as SimpleTextBox,
  SimpleTitleLabel_default as SimpleTitleLabel,
  Sizer_default as Sizer,
  Skew_default as Skew,
  Slider_default as Slider,
  Space_default as Space,
  SplitPanels_default as SplitPanels,
  StatesBarRectangle_default as StatesBarRectangle,
  StatesBitmapText_default as StatesBitmapText,
  StatesImage_default as StatesImage,
  StatesNinePatch_default as StatesNinePatch,
  StatesNineSlice_default as StatesNineSlice,
  StatesRoundRectangle_default as StatesRoundRectangle,
  StatesText_default as StatesText,
  Swipe_default as Swipe,
  TabPages_default as TabPages,
  Tabs_default as Tabs,
  TagText_default as TagText,
  Tap_default as Tap,
  TextArea_default as TextArea,
  TextAreaInput_default as TextAreaInput,
  TextBox_default as TextBox,
  TextEdit_default as TextEdit,
  TextPage_default as TextPage,
  TextPlayer_default as TextPlayer,
  TextTyping_default as TextTyping,
  Triangle_default as Tirangle,
  TitleLabel_default as TitleLabel,
  Toast_default as Toast,
  ToastQueue_default as ToastQueue,
  ToggleSwitch_default as ToggleSwitch,
  TouchEventStop_default as TouchEventStop,
  TransitionImage_default as TransitionImage,
  TransitionImagePack_default as TransitionImagePack,
  Trees_default as Trees,
  Tweaker_default as Tweaker,
  WaitComplete,
  WaitEvent,
  WrapExpandText_default as WrapExpandText
};
//# sourceMappingURL=phaser3-rex-plugins_templates_ui_ui-components__js.js.map
