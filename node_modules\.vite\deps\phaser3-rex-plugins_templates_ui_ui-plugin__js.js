import {
  <PERSON><PERSON><PERSON>ner_default,
  AlphaMask<PERSON>mage_default,
  Anchor_default,
  BBCodeText_default,
  BadgeLabel_default,
  Buttons_default,
  CanvasInput_default,
  Canvas_default,
  Chart_default,
  Checkbox_default,
  CircleMaskImage_default,
  CircularProgressCanvas_default,
  CircularProgress_default,
  ClickOutside_default,
  Click_default,
  ColorComponents_default,
  ColorInputBase_default,
  ColorInput_default,
  ColorPicker_default,
  ConfirmActionButton_default,
  ConfirmAction_default,
  ConfirmDialog_default,
  Container_default,
  Cover_default,
  CustomProgress_default,
  CustomShapes_default,
  Delay_default,
  Dialog_default,
  Drag_default,
  DropDownList_default,
  DynamicText_default,
  EaseMoveFrom_default,
  EaseMoveTo_default,
  Edit_default,
  ExpBar_default,
  FileChooser_default,
  FileDropZone_default,
  FileSelectorButton_default,
  FixWidthButtons_default,
  FixWidthSizer_default,
  Flip_default,
  Folder_default,
  FontSizeExpandText_default,
  FontSizeFit_default,
  FullWindowRectangle_default,
  FullWindowZone_default,
  GetParent,
  GetTopmostParent,
  GetViewport_default,
  GridButtons_default,
  GridSizer_default,
  GridTable_default,
  HiddenEdit_default,
  Hide,
  HolyGrail_default,
  ImageBox_default,
  ImageInputLabel_default,
  InTouching_default,
  InputText_default,
  IsGameObject_default,
  IsPointerInBounds_default,
  IsShown,
  Knob_default,
  Label_default,
  LayerManager_default,
  LineProgressCanvas_default,
  LineProgress_default,
  Menu_default,
  Modal,
  ModalClose,
  ModalPromise,
  NameInputDialog_default,
  NameValueLabel_default,
  NinePatch_default,
  NinePatch_default2,
  NumberBar_default,
  Open_default,
  OverlapSizer_default,
  Pages_default,
  Pan_default,
  PerspectiveCard_default,
  Perspective_default,
  Pinch_default,
  Press_default,
  QuadShape_default,
  RemoveFromParent_default,
  RequestDrag_default,
  Rotate_default,
  RoundRectangleCanvas_default,
  RoundRectangleProgress_default,
  RoundRectangle_default,
  ScrollBar_default,
  ScrollablePanel_default,
  SetChildrenInteractive_default,
  SetValue_default,
  Shake_default,
  Show,
  Sides_default,
  SimpleDropDownList_default,
  SimpleLabel_default,
  SimpleTextBox_default,
  SimpleTitleLabel_default,
  Sizer_default,
  Skew_default,
  Slider_default,
  Space_default,
  SplitPanels_default,
  StatesBarRectangle_default,
  StatesBitmapText_default,
  StatesImage_default,
  StatesNinePatch_default,
  StatesNineSlice_default,
  StatesRoundRectangle_default,
  StatesText_default,
  Swipe_default,
  TabPages_default,
  Tabs_default,
  TagText_default,
  Tap_default,
  TextAreaInput_default,
  TextArea_default,
  TextBox_default,
  TextEdit_default,
  TextPage_default,
  TextPlayer_default,
  TextTyping_default,
  TitleLabel_default,
  ToastQueue_default,
  Toast_default,
  ToggleSwitch_default,
  TouchEventStop_default,
  TransitionImagePack_default,
  TransitionImage_default,
  Trees_default,
  Triangle_default,
  Tweaker_default,
  WaitComplete,
  WaitEvent,
  WrapExpandText_default,
  fade_in_default,
  fade_out_destroy_default
} from "./chunk-KH3HNOE5.js";
import "./chunk-5WRI5ZAA.js";

// node_modules/phaser3-rex-plugins/templates/ui/ObjectFactory.js
var ObjectFactory = class _ObjectFactory {
  constructor(scene) {
    this.scene = scene;
    scene.events.once("destroy", this.destroy, this);
  }
  destroy() {
    this.scene = null;
  }
  static register(type, callback) {
    _ObjectFactory.prototype[type] = callback;
  }
};
var ObjectFactory_default = ObjectFactory;

// node_modules/phaser3-rex-plugins/templates/ui/ninepatch/Factory.js
ObjectFactory_default.register("ninePatch", function(x, y, width, height, key, columns, rows, config) {
  var gameObject = new NinePatch_default(this.scene, x, y, width, height, key, columns, rows, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.NinePatch", NinePatch_default);

// node_modules/phaser3-rex-plugins/templates/ui/ninepatch2/Factory.js
ObjectFactory_default.register("ninePatch2", function(x, y, width, height, key, columns, rows, config) {
  var gameObject = new NinePatch_default2(this.scene, x, y, width, height, key, columns, rows, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.NinePatch2", NinePatch_default2);

// node_modules/phaser3-rex-plugins/templates/ui/roundrectangle/Factory.js
ObjectFactory_default.register("roundRectangle", function(x, y, width, height, radiusConfig, fillColor, fillAlpha) {
  var gameObject = new RoundRectangle_default(this.scene, x, y, width, height, radiusConfig, fillColor, fillAlpha);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.RoundRectangle", RoundRectangle_default);

// node_modules/phaser3-rex-plugins/templates/ui/roundrectanglecanvas/Factory.js
ObjectFactory_default.register("roundRectangleCanvas", function(x, y, width, height, radius, fillStyle, strokeStyle, lineWidth, fillColor2, isHorizontalGradient) {
  var gameObject = new RoundRectangleCanvas_default(this.scene, x, y, width, height, radius, fillStyle, strokeStyle, lineWidth, fillColor2, isHorizontalGradient);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.RoundRectangleCanvas", RoundRectangleCanvas_default);

// node_modules/phaser3-rex-plugins/templates/ui/quadshape/Factory.js
ObjectFactory_default.register("QuadShape", function(x, y, width, height, fillColor, fillAlpha) {
  var gameObject = new QuadShape_default(this.scene, x, y, width, height, fillColor, fillAlpha);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.QuadShape", QuadShape_default);

// node_modules/phaser3-rex-plugins/templates/ui/bbcodetext/Factory.js
ObjectFactory_default.register("BBCodeText", function(x, y, text, style) {
  var gameObject = new BBCodeText_default(this.scene, x, y, text, style);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.BBCodeText", BBCodeText_default);

// node_modules/phaser3-rex-plugins/templates/ui/tagtext/Factory.js
ObjectFactory_default.register("tagText", function(x, y, text, style) {
  var gameObject = new TagText_default(this.scene, x, y, text, style);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TagText", TagText_default);

// node_modules/phaser3-rex-plugins/templates/ui/dynamictext/Factory.js
ObjectFactory_default.register("dynamicText", function(x, y, width, height, config) {
  var gameObject = new DynamicText_default(this.scene, x, y, width, height, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.DynamicText", DynamicText_default);

// node_modules/phaser3-rex-plugins/templates/ui/textplayer/Factory.js
ObjectFactory_default.register("textPlayer", function(x, y, width, height, config) {
  var gameObject = new TextPlayer_default(this.scene, x, y, width, height, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TextPlayer", TextPlayer_default);

// node_modules/phaser3-rex-plugins/templates/ui/canvasinput/Factory.js
ObjectFactory_default.register("canvasInput", function(x, y, fixedWidth, fixedHeight, config) {
  var gameObject = new CanvasInput_default(this.scene, x, y, fixedWidth, fixedHeight, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.CanvasInput", CanvasInput_default);

// node_modules/phaser3-rex-plugins/templates/ui/hiddenedit/Factory.js
ObjectFactory_default.register("hiddenEdit", function(textObject, config) {
  var gameObject = new HiddenEdit_default(textObject, config);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.HiddenEdit", HiddenEdit_default);

// node_modules/phaser3-rex-plugins/templates/ui/checkbox/Factory.js
ObjectFactory_default.register("checkbox", function(x, y, width, height, color, config) {
  var gameObject = new Checkbox_default(this.scene, x, y, width, height, color, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Checkbox", Checkbox_default);

// node_modules/phaser3-rex-plugins/templates/ui/toggleswitch/Factory.js
ObjectFactory_default.register("toggleSwitch", function(x, y, width, height, color, config) {
  var gameObject = new ToggleSwitch_default(this.scene, x, y, width, height, color, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ToggleSwitch", ToggleSwitch_default);

// node_modules/phaser3-rex-plugins/templates/ui/canvas/Factory.js
ObjectFactory_default.register("canvas", function(x, y, width, height) {
  var gameObject = new Canvas_default(this.scene, x, y, width, height);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Canvas", Canvas_default);

// node_modules/phaser3-rex-plugins/templates/ui/circlemaskimage/Factory.js
ObjectFactory_default.register("circleMaskImage", function(x, y, key, frame, config) {
  var gameObject = new CircleMaskImage_default(this.scene, x, y, key, frame, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.CircleMaskImage", CircleMaskImage_default);

// node_modules/phaser3-rex-plugins/templates/ui/alphamaskimage/Factory.js
ObjectFactory_default.register("alphaMaskImage", function(x, y, key, frame, config) {
  var gameObject = new AlphaMaskImage_default(this.scene, x, y, key, frame, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.AlphaMaskImage", AlphaMaskImage_default);

// node_modules/phaser3-rex-plugins/templates/ui/circularprogress/Factory.js
ObjectFactory_default.register("circularProgress", function(x, y, radius, barColor, value, config) {
  var gameObject = new CircularProgress_default(this.scene, x, y, radius, barColor, value, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.CircularProgress", CircularProgress_default);

// node_modules/phaser3-rex-plugins/templates/ui/circularprogresscanvas/Factory.js
ObjectFactory_default.register("circularProgressCanvas", function(x, y, radius, barColor, value, config) {
  var gameObject = new CircularProgressCanvas_default(this.scene, x, y, radius, barColor, value, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.CircularProgressCanvas", CircularProgressCanvas_default);

// node_modules/phaser3-rex-plugins/templates/ui/lineprogress/Factory.js
ObjectFactory_default.register("lineProgress", function(x, y, width, height, barColor, value, config) {
  var gameObject = new LineProgress_default(this.scene, x, y, width, height, barColor, value, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.LineProgress", LineProgress_default);

// node_modules/phaser3-rex-plugins/templates/ui/roundrectangleprogress/Factory.js
ObjectFactory_default.register("roundRectanleProgress", function(x, y, width, height, radiusConfig, barColor, value, config) {
  var gameObject = new RoundRectangleProgress_default(this.scene, x, y, width, height, radiusConfig, barColor, value, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.RoundRectangleProgress", RoundRectangleProgress_default);

// node_modules/phaser3-rex-plugins/templates/ui/lineprogresscanvas/Factory.js
ObjectFactory_default.register("circularProgressCanvas", function(x, y, width, height, barColor, value, config) {
  var gameObject = new LineProgressCanvas_default(this.scene, x, y, width, height, barColor, value, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.LineProgressCanvas", LineProgressCanvas_default);

// node_modules/phaser3-rex-plugins/templates/ui/triangle/Factory.js
ObjectFactory_default.register("triangle", function(x, y, width, height, fillColor, fillAlpha) {
  var gameObject = new Triangle_default(this.scene, x, y, width, height, fillColor, fillAlpha);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Triangle", Triangle_default);

// node_modules/phaser3-rex-plugins/templates/ui/knob/Factory.js
ObjectFactory_default.register("knob", function(config) {
  var gameObject = new Knob_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Knob", Knob_default);

// node_modules/phaser3-rex-plugins/templates/ui/customshapes/Factory.js
ObjectFactory_default.register("customShapes", function(x, y, width, height, config) {
  var gameObject = new CustomShapes_default(this.scene, x, y, width, height, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.CustomShapes", CustomShapes_default);

// node_modules/phaser3-rex-plugins/templates/ui/customprogress/Factory.js
ObjectFactory_default.register("customProgress", function(x, y, width, height, config) {
  var gameObject = new CustomProgress_default(this.scene, x, y, width, height, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.CustomProgress", CustomProgress_default);

// node_modules/phaser3-rex-plugins/templates/ui/aiospinner/Factory.js
ObjectFactory_default.register("aioSpinner", function(config) {
  var gameObject = new AIOSpinner_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.AIOSpinner", AIOSpinner_default);

// node_modules/phaser3-rex-plugins/templates/ui/transitionimage/Factory.js
ObjectFactory_default.register("transitionImage", function(x, y, texture, frame, config) {
  var gameObject = new TransitionImage_default(this.scene, x, y, texture, frame, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TransitionImage", TransitionImage_default);

// node_modules/phaser3-rex-plugins/templates/ui/transitionimagepack/Factory.js
ObjectFactory_default.register("transitionImagePack", function(x, y, texture, frame, config) {
  var gameObject = new TransitionImagePack_default(this.scene, x, y, texture, frame, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TransitionImagePack", TransitionImagePack_default);

// node_modules/phaser3-rex-plugins/templates/ui/imagebox/Factory.js
ObjectFactory_default.register("imageBox", function(x, y, texture, frame, config) {
  var gameObject = new ImageBox_default(this.scene, x, y, texture, frame, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ImageBox", ImageBox_default);

// node_modules/phaser3-rex-plugins/templates/ui/fullwindowrectangle/Factory.js
ObjectFactory_default.register("fullWindowRectangle", function(fillColor, fillAlpha) {
  var gameObject = new FullWindowRectangle_default(this.scene, fillColor, fillAlpha);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FullWindowRectangle", FullWindowRectangle_default);

// node_modules/phaser3-rex-plugins/templates/ui/fullwindowzone/Factory.js
ObjectFactory_default.register("fullWindowZone", function() {
  var gameObject = new FullWindowZone_default(this.scene);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FullWindowZone", FullWindowZone_default);

// node_modules/phaser3-rex-plugins/templates/ui/cover/Factory.js
ObjectFactory_default.register("cover", function(config) {
  var gameObject = new Cover_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Cover", Cover_default);

// node_modules/phaser3-rex-plugins/templates/ui/inputtext/Factory.js
ObjectFactory_default.register("inputText", function(config) {
  var gameObject = new InputText_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.InputText", InputText_default);

// node_modules/phaser3-rex-plugins/templates/ui/filechooser/Factory.js
ObjectFactory_default.register("fileChooser", function(config) {
  var gameObject = new FileChooser_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FileChooser", FileChooser_default);

// node_modules/phaser3-rex-plugins/templates/ui/filedropzone/Factory.js
ObjectFactory_default.register("fileDropZone", function(config) {
  var gameObject = new FileDropZone_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FileDropZone", FileDropZone_default);

// node_modules/phaser3-rex-plugins/templates/ui/imageinputlabel/Factory.js
ObjectFactory_default.register("imageInputLabel", function(config) {
  var gameObject = new ImageInputLabel_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ImageInputLabel", ImageInputLabel_default);

// node_modules/phaser3-rex-plugins/templates/ui/statesimage/Factory.js
ObjectFactory_default.register("statesImage", function(config) {
  var gameObject = new StatesImage_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesImage", StatesImage_default);

// node_modules/phaser3-rex-plugins/templates/ui/statesroundrectangle/Factory.js
ObjectFactory_default.register("statesRoundRectangle", function(config) {
  var gameObject = new StatesRoundRectangle_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesRoundRectangle", StatesRoundRectangle_default);

// node_modules/phaser3-rex-plugins/templates/ui/statesnineslice/Factory.js
ObjectFactory_default.register("statesNineSlice", function(config) {
  var gameObject = new StatesNineSlice_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesNineSlice", StatesNineSlice_default);

// node_modules/phaser3-rex-plugins/templates/ui/statesninepatch/Factory.js
ObjectFactory_default.register("statesNinePatch", function(config) {
  var gameObject = new StatesNinePatch_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesNinePatch", StatesNinePatch_default);

// node_modules/phaser3-rex-plugins/templates/ui/statestext/Factory.js
ObjectFactory_default.register("statesText", function(config) {
  var gameObject = new StatesText_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesText", StatesText_default);

// node_modules/phaser3-rex-plugins/templates/ui/statesbitmaptext/Factory.js
ObjectFactory_default.register("statesBitmapText", function(config) {
  var gameObject = new StatesBitmapText_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesBitmapText", StatesBitmapText_default);

// node_modules/phaser3-rex-plugins/templates/ui/statesbarrectangle/Factory.js
ObjectFactory_default.register("statesBarRectangle", function(config) {
  var gameObject = new StatesBarRectangle_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.StatesBarRectangle", StatesBarRectangle_default);

// node_modules/phaser3-rex-plugins/templates/ui/chart/Factory.js
ObjectFactory_default.register("chart", function(x, y, width, height, config) {
  var gameObject = new Chart_default(this.scene, x, y, width, height, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Chart", Chart_default);

// node_modules/phaser3-rex-plugins/templates/ui/container/Factory.js
ObjectFactory_default.register("container", function(x, y, width, height, children) {
  var gameObject = new Container_default(this.scene, x, y, width, height, children);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Container", Container_default);

// node_modules/phaser3-rex-plugins/templates/ui/sizer/Factory.js
ObjectFactory_default.register("sizer", function(x, y, minWidth, minHeight, orientation, config) {
  var gameObject = new Sizer_default(this.scene, x, y, minWidth, minHeight, orientation, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Sizer", Sizer_default);

// node_modules/phaser3-rex-plugins/templates/ui/gridsizer/Factory.js
ObjectFactory_default.register("gridSizer", function(x, y, minWidth, minHeight, columnCount, rowCount, columnProportions, rowProportion, config) {
  var gameObject = new GridSizer_default(this.scene, x, y, minWidth, minHeight, columnCount, rowCount, columnProportions, rowProportion, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.GridSizer", GridSizer_default);

// node_modules/phaser3-rex-plugins/templates/ui/fixwidthsizer/Factory.js
ObjectFactory_default.register("fixWidthSizer", function(x, y, minWidth, minHeight, config) {
  var gameObject = new FixWidthSizer_default(this.scene, x, y, minWidth, minHeight, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FixWidthSizer", FixWidthSizer_default);

// node_modules/phaser3-rex-plugins/templates/ui/overlapsizer/Factory.js
ObjectFactory_default.register("overlapSizer", function(x, y, minWidth, minHeight, config) {
  var gameObject = new OverlapSizer_default(this.scene, x, y, minWidth, minHeight, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.OverlapSizer", OverlapSizer_default);

// node_modules/phaser3-rex-plugins/templates/ui/space/Factory.js
ObjectFactory_default.register("space", function() {
  var gameObject = new Space_default(this.scene);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Space", Space_default);

// node_modules/phaser3-rex-plugins/templates/ui/label/Factory.js
ObjectFactory_default.register("label", function(config) {
  var gameObject = new Label_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Label", Label_default);

// node_modules/phaser3-rex-plugins/templates/ui/simplelabel/Factory.js
ObjectFactory_default.register("simpleLabel", function(config, creators) {
  var gameObject = new SimpleLabel_default(this.scene, config, creators);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.SimpleLabel", SimpleLabel_default);

// node_modules/phaser3-rex-plugins/templates/ui/titlelabel/Factory.js
ObjectFactory_default.register("titleLabel", function(config) {
  var gameObject = new TitleLabel_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TitleLabel", TitleLabel_default);

// node_modules/phaser3-rex-plugins/templates/ui/simpletitlelabel/Factory.js
ObjectFactory_default.register("simpleTitleLabel", function(config, creators) {
  var gameObject = new SimpleTitleLabel_default(this.scene, config, creators);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.SimpleTitleLabel", SimpleTitleLabel_default);

// node_modules/phaser3-rex-plugins/templates/ui/namevaluelabel/Factory.js
ObjectFactory_default.register("nameValueLabel", function(config) {
  var gameObject = new NameValueLabel_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.NameValueLabel", NameValueLabel_default);

// node_modules/phaser3-rex-plugins/templates/ui/expbar/Factory.js
ObjectFactory_default.register("expBar", function(config) {
  var gameObject = new ExpBar_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ExpBar", ExpBar_default);

// node_modules/phaser3-rex-plugins/templates/ui/buttons/Factory.js
ObjectFactory_default.register("buttons", function(config) {
  var gameObject = new Buttons_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Buttons", Buttons_default);

// node_modules/phaser3-rex-plugins/templates/ui/gridbuttons/Factory.js
ObjectFactory_default.register("gridButtons", function(config) {
  var gameObject = new GridButtons_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.GridButtons", GridButtons_default);

// node_modules/phaser3-rex-plugins/templates/ui/fixwidthbuttons/Factory.js
ObjectFactory_default.register("fixWidthButtons", function(config) {
  var gameObject = new FixWidthButtons_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FixWidthButtons", FixWidthButtons_default);

// node_modules/phaser3-rex-plugins/templates/ui/fileselectorbutton/Factory.js
ObjectFactory_default.register("fileSelectorButton", function(config) {
  var gameObject = new FileSelectorButton_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.FileSelectorButton", FileSelectorButton_default);

// node_modules/phaser3-rex-plugins/templates/ui/dialog/Factory.js
ObjectFactory_default.register("dialog", function(config) {
  var gameObject = new Dialog_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Dialog", Dialog_default);

// node_modules/phaser3-rex-plugins/templates/ui/confirmdialog/Factory.js
ObjectFactory_default.register("confirmDialog", function(config, creators) {
  var gameObject = new ConfirmDialog_default(this.scene, config, creators);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ConfirmDialog", ConfirmDialog_default);

// node_modules/phaser3-rex-plugins/templates/ui/confirmactionbutton/Factory.js
ObjectFactory_default.register("confirmActionButton", function(config) {
  var gameObject = new ConfirmActionButton_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ConfirmActionButton", ConfirmActionButton_default);

// node_modules/phaser3-rex-plugins/templates/ui/nameinputdialog/Factory.js
ObjectFactory_default.register("nameInputDialog", function(config, creators) {
  var gameObject = new NameInputDialog_default(this.scene, config, creators);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.NameInputDialog", NameInputDialog_default);

// node_modules/phaser3-rex-plugins/templates/ui/holygrail/Factory.js
ObjectFactory_default.register("holyGrail", function(config) {
  var gameObject = new HolyGrail_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.HolyGrail", HolyGrail_default);

// node_modules/phaser3-rex-plugins/templates/ui/tabs/Factory.js
ObjectFactory_default.register("tabs", function(config) {
  var gameObject = new Tabs_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Tabs", Tabs_default);

// node_modules/phaser3-rex-plugins/templates/ui/slider/Factory.js
ObjectFactory_default.register("slider", function(config) {
  var gameObject = new Slider_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Slider", Slider_default);

// node_modules/phaser3-rex-plugins/templates/ui/gridtable/Factory.js
ObjectFactory_default.register("gridTable", function(config) {
  var gameObject = new GridTable_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.GridTable", GridTable_default);

// node_modules/phaser3-rex-plugins/templates/ui/menu/Factory.js
ObjectFactory_default.register("menu", function(config) {
  var gameObject = new Menu_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Menu", Menu_default);

// node_modules/phaser3-rex-plugins/templates/ui/dropdownlist/Factory.js
ObjectFactory_default.register("dropDownList", function(config) {
  var gameObject = new DropDownList_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.DropDownList", DropDownList_default);

// node_modules/phaser3-rex-plugins/templates/ui/simpledropdownlist/Factory.js
ObjectFactory_default.register("simpleDropDownList", function(config, creators) {
  var gameObject = new SimpleDropDownList_default(this.scene, config, creators);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.SimpleDropDownList", SimpleDropDownList_default);

// node_modules/phaser3-rex-plugins/templates/ui/textbox/Factory.js
ObjectFactory_default.register("textBox", function(config) {
  var gameObject = new TextBox_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TextBox", TextBox_default);

// node_modules/phaser3-rex-plugins/templates/ui/simpletextbox/Factory.js
ObjectFactory_default.register("simpleTextBox", function(config) {
  var gameObject = new SimpleTextBox_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.SimpleTextBox", SimpleTextBox_default);

// node_modules/phaser3-rex-plugins/templates/ui/numberbar/Factory.js
ObjectFactory_default.register("numberBar", function(config) {
  var gameObject = new NumberBar_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.NumberBar", NumberBar_default);

// node_modules/phaser3-rex-plugins/templates/ui/scrollbar/Factory.js
ObjectFactory_default.register("scrollBar", function(config) {
  var gameObject = new ScrollBar_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ScrollBar", ScrollBar_default);

// node_modules/phaser3-rex-plugins/templates/ui/badgelabel/Factory.js
ObjectFactory_default.register("badgeLabel", function(config) {
  var gameObject = new BadgeLabel_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.BadgeLabel", BadgeLabel_default);

// node_modules/phaser3-rex-plugins/templates/ui/pages/Factory.js
ObjectFactory_default.register("pages", function(config) {
  var gameObject = new Pages_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Pages", Pages_default);

// node_modules/phaser3-rex-plugins/templates/ui/perspectivecard/Factory.js
ObjectFactory_default.register("perspectiveCard", function(config) {
  var gameObject = new PerspectiveCard_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.PerspectiveCard", PerspectiveCard_default);

// node_modules/phaser3-rex-plugins/templates/ui/tabpages/Factory.js
ObjectFactory_default.register("tabPages", function(config) {
  var gameObject = new TabPages_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TabPages", TabPages_default);

// node_modules/phaser3-rex-plugins/templates/ui/folder/Factory.js
ObjectFactory_default.register("folder", function(config) {
  var gameObject = new Folder_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Folder", Folder_default);

// node_modules/phaser3-rex-plugins/templates/ui/trees/Factory.js
ObjectFactory_default.register("trees", function(config) {
  var gameObject = new Trees_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Trees", Trees_default);

// node_modules/phaser3-rex-plugins/templates/ui/textarea/Factory.js
ObjectFactory_default.register("textArea", function(config) {
  var gameObject = new TextArea_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TextArea", TextArea_default);

// node_modules/phaser3-rex-plugins/templates/ui/textareainput/Factory.js
ObjectFactory_default.register("textAreaInput", function(config) {
  var gameObject = new TextAreaInput_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.TextAreaInput", TextAreaInput_default);

// node_modules/phaser3-rex-plugins/templates/ui/scrollablepanel/Factory.js
ObjectFactory_default.register("scrollablePanel", function(config) {
  var gameObject = new ScrollablePanel_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ScrollablePanel", ScrollablePanel_default);

// node_modules/phaser3-rex-plugins/templates/ui/toast/Factory.js
ObjectFactory_default.register("toast", function(config) {
  var gameObject = new Toast_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Toast", Toast_default);

// node_modules/phaser3-rex-plugins/templates/ui/toastqueue/Factory.js
ObjectFactory_default.register("toastQueue", function(config) {
  var gameObject = new ToastQueue_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ToastQueue", ToastQueue_default);

// node_modules/phaser3-rex-plugins/templates/ui/colorinput/colorinput/Factory.js
ObjectFactory_default.register("colorInput", function(config) {
  var gameObject = new ColorInput_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ColorInput", ColorInput_default);

// node_modules/phaser3-rex-plugins/templates/ui/colorinput/colorinputbase/Factory.js
ObjectFactory_default.register("colorInputLite", function(config) {
  var gameObject = new ColorInputBase_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ColorInputBase", ColorInputBase_default);

// node_modules/phaser3-rex-plugins/templates/ui/colorinput/colorpicker/Factory.js
ObjectFactory_default.register("colorPicker", function(config) {
  var gameObject = new ColorPicker_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ColorPicker", ColorPicker_default);

// node_modules/phaser3-rex-plugins/templates/ui/colorinput/colorcomponents/Factory.js
ObjectFactory_default.register("colorComponents", function(config) {
  var gameObject = new ColorComponents_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.ColorComponents", ColorComponents_default);

// node_modules/phaser3-rex-plugins/templates/ui/splitpanels/Factory.js
ObjectFactory_default.register("splitPanels", function(config) {
  var gameObject = new SplitPanels_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.SplitPanels", SplitPanels_default);

// node_modules/phaser3-rex-plugins/templates/ui/sides/Factory.js
ObjectFactory_default.register("sides", function(config) {
  var gameObject = new Sides_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Sides", Sides_default);

// node_modules/phaser3-rex-plugins/templates/ui/tweaker/Factory.js
ObjectFactory_default.register("tweaker", function(config) {
  var gameObject = new Tweaker_default(this.scene, config);
  this.scene.add.existing(gameObject);
  return gameObject;
});
SetValue_default(window, "RexPlugins.UI.Tweaker", Tweaker_default);

// node_modules/phaser3-rex-plugins/templates/ui/click/Factory.js
ObjectFactory_default.register("click", function(gameObject, config) {
  return new Click_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Click", Click_default);

// node_modules/phaser3-rex-plugins/templates/ui/clickoutside/Factory.js
ObjectFactory_default.register("clickOutside", function(gameObject, config) {
  return new ClickOutside_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.ClickOutside", ClickOutside_default);

// node_modules/phaser3-rex-plugins/templates/ui/intouching/Factory.js
ObjectFactory_default.register("inTouching", function(gameObject, config) {
  return new InTouching_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.InTouching", InTouching_default);

// node_modules/phaser3-rex-plugins/templates/ui/tap/Factory.js
ObjectFactory_default.register("tap", function(gameObject, config) {
  if (!IsGameObject_default(gameObject)) {
    config = gameObject;
    gameObject = this.scene;
  }
  return new Tap_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Tap", Tap_default);

// node_modules/phaser3-rex-plugins/templates/ui/press/Factory.js
ObjectFactory_default.register("press", function(gameObject, config) {
  if (!IsGameObject_default(gameObject)) {
    config = gameObject;
    gameObject = this.scene;
  }
  return new Press_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Press", Press_default);

// node_modules/phaser3-rex-plugins/templates/ui/swipe/Factory.js
ObjectFactory_default.register("swipe", function(gameObject, config) {
  if (!IsGameObject_default(gameObject)) {
    config = gameObject;
    gameObject = this.scene;
  }
  return new Swipe_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Swipe", Swipe_default);

// node_modules/phaser3-rex-plugins/templates/ui/pan/Factory.js
ObjectFactory_default.register("pan", function(gameObject, config) {
  if (!IsGameObject_default(gameObject)) {
    config = gameObject;
    gameObject = this.scene;
  }
  return new Pan_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Pan", Pan_default);

// node_modules/phaser3-rex-plugins/templates/ui/drag/Factory.js
ObjectFactory_default.register("drag", function(gameObject, config) {
  return new Drag_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Drag", Drag_default);

// node_modules/phaser3-rex-plugins/templates/ui/pinch/Factory.js
ObjectFactory_default.register("pinch", function(config) {
  return new Pinch_default(this.scene, config);
});
SetValue_default(window, "RexPlugins.UI.Pinch", Pinch_default);

// node_modules/phaser3-rex-plugins/templates/ui/rotate/Factory.js
ObjectFactory_default.register("rotate", function(config) {
  return new Rotate_default(this.scene, config);
});
SetValue_default(window, "RexPlugins.UI.Rotate", Rotate_default);

// node_modules/phaser3-rex-plugins/templates/ui/flip/Factory.js
ObjectFactory_default.register("flip", function(gameObject, config) {
  return new Flip_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Flip", Flip_default);

// node_modules/phaser3-rex-plugins/templates/ui/shake/Factory.js
ObjectFactory_default.register("shake", function(gameObject, config) {
  return new Shake_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Shake", Shake_default);

// node_modules/phaser3-rex-plugins/templates/ui/toucheventstop/Factory.js
ObjectFactory_default.register("touchEventStop", function(gameObject, config) {
  return new TouchEventStop_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.TouchEventStop", TouchEventStop_default);

// node_modules/phaser3-rex-plugins/templates/ui/perspective/Factory.js
ObjectFactory_default.register("perspective", function(gameObject, config) {
  return new Perspective_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Perspective", Perspective_default);

// node_modules/phaser3-rex-plugins/templates/ui/skew/Factory.js
ObjectFactory_default.register("skew", function(gameObject, config) {
  return new Skew_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Skew", Skew_default);

// node_modules/phaser3-rex-plugins/templates/ui/anchor/Factory.js
ObjectFactory_default.register("anchor", function(gameObject, config) {
  return new Anchor_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.Anchor", Anchor_default);

// node_modules/phaser3-rex-plugins/templates/ui/texttyping/Factory.js
ObjectFactory_default.register("textTyping", function(gameObject, config) {
  return new TextTyping_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.TextTyping", TextTyping_default);

// node_modules/phaser3-rex-plugins/templates/ui/textpage/Factory.js
ObjectFactory_default.register("textPage", function(gameObject, config) {
  return new TextPage_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.TextPage", TextPage_default);

// node_modules/phaser3-rex-plugins/templates/ui/textedit/Factory.js
ObjectFactory_default.register("textEdit", function(gameObject, config) {
  return new TextEdit_default(gameObject, config);
});
SetValue_default(window, "RexPlugins.UI.TextEdit", TextEdit_default);

// node_modules/phaser3-rex-plugins/templates/ui/layermanager/Factory.js
ObjectFactory_default.register("layerManager", function(config) {
  return new LayerManager_default(this.scene, config);
});
SetValue_default(window, "RexPlugins.UI.LayerManager", LayerManager_default);

// node_modules/phaser3-rex-plugins/templates/ui/ui-plugin.js
var UIPlugin = class extends Phaser.Plugins.ScenePlugin {
  constructor(scene, pluginManager) {
    super(scene, pluginManager);
    this.add = new ObjectFactory_default(scene);
  }
  boot() {
    var eventEmitter = this.scene.events;
    eventEmitter.on("destroy", this.destroy, this);
  }
  destroy() {
    this.add.destroy();
    super.destroy();
  }
  isInTouching(gameObject, pointer, preTest, postTest) {
    if (!gameObject.visible) {
      return false;
    }
    return IsPointerInBounds_default(gameObject, pointer, preTest, postTest);
  }
  get viewport() {
    return GetViewport_default(this.scene, this.scene.cameras.main, true);
  }
};
var methods = {
  getParentSizer: GetParent,
  getTopmostSizer: GetTopmostParent,
  removeFromParent: RemoveFromParent_default,
  hide: Hide,
  show: Show,
  isShown: IsShown,
  confirmAction: ConfirmAction_default,
  edit: Edit_default,
  wrapExpandText: WrapExpandText_default,
  fontSizeExpandText: FontSizeExpandText_default,
  fontSizeResize: FontSizeFit_default,
  // Backward compatibility
  setFontSizeToFitWidth: FontSizeFit_default,
  waitEvent: WaitEvent,
  waitComplete: WaitComplete,
  delayPromise: Delay_default,
  setChildrenInteractive: SetChildrenInteractive_default,
  fadeIn: fade_in_default,
  fadeOutDestroy: fade_out_destroy_default,
  easeMoveTo: EaseMoveTo_default,
  easeMoveFrom: EaseMoveFrom_default,
  modal: Modal,
  modalPromise: ModalPromise,
  modalClose: ModalClose,
  requestDrag: RequestDrag_default,
  openFileChooser: Open_default
};
Object.assign(
  UIPlugin.prototype,
  methods
);
var ui_plugin_default = UIPlugin;
export {
  ui_plugin_default as default
};
//# sourceMappingURL=phaser3-rex-plugins_templates_ui_ui-plugin__js.js.map
