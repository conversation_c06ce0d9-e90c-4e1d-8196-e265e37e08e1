{"version": 3, "sources": ["../../phaser3-rex-plugins/templates/ui/ObjectFactory.js", "../../phaser3-rex-plugins/templates/ui/ninepatch/Factory.js", "../../phaser3-rex-plugins/templates/ui/ninepatch2/Factory.js", "../../phaser3-rex-plugins/templates/ui/roundrectangle/Factory.js", "../../phaser3-rex-plugins/templates/ui/roundrectanglecanvas/Factory.js", "../../phaser3-rex-plugins/templates/ui/quadshape/Factory.js", "../../phaser3-rex-plugins/templates/ui/bbcodetext/Factory.js", "../../phaser3-rex-plugins/templates/ui/tagtext/Factory.js", "../../phaser3-rex-plugins/templates/ui/dynamictext/Factory.js", "../../phaser3-rex-plugins/templates/ui/textplayer/Factory.js", "../../phaser3-rex-plugins/templates/ui/canvasinput/Factory.js", "../../phaser3-rex-plugins/templates/ui/hiddenedit/Factory.js", "../../phaser3-rex-plugins/templates/ui/checkbox/Factory.js", "../../phaser3-rex-plugins/templates/ui/toggleswitch/Factory.js", "../../phaser3-rex-plugins/templates/ui/canvas/Factory.js", "../../phaser3-rex-plugins/templates/ui/circlemaskimage/Factory.js", "../../phaser3-rex-plugins/templates/ui/alphamaskimage/Factory.js", "../../phaser3-rex-plugins/templates/ui/circularprogress/Factory.js", "../../phaser3-rex-plugins/templates/ui/circularprogresscanvas/Factory.js", "../../phaser3-rex-plugins/templates/ui/lineprogress/Factory.js", "../../phaser3-rex-plugins/templates/ui/roundrectangleprogress/Factory.js", "../../phaser3-rex-plugins/templates/ui/lineprogresscanvas/Factory.js", "../../phaser3-rex-plugins/templates/ui/triangle/Factory.js", "../../phaser3-rex-plugins/templates/ui/knob/Factory.js", "../../phaser3-rex-plugins/templates/ui/customshapes/Factory.js", "../../phaser3-rex-plugins/templates/ui/customprogress/Factory.js", "../../phaser3-rex-plugins/templates/ui/aiospinner/Factory.js", "../../phaser3-rex-plugins/templates/ui/transitionimage/Factory.js", "../../phaser3-rex-plugins/templates/ui/transitionimagepack/Factory.js", "../../phaser3-rex-plugins/templates/ui/imagebox/Factory.js", "../../phaser3-rex-plugins/templates/ui/fullwindowrectangle/Factory.js", "../../phaser3-rex-plugins/templates/ui/fullwindowzone/Factory.js", "../../phaser3-rex-plugins/templates/ui/cover/Factory.js", "../../phaser3-rex-plugins/templates/ui/inputtext/Factory.js", "../../phaser3-rex-plugins/templates/ui/filechooser/Factory.js", "../../phaser3-rex-plugins/templates/ui/filedropzone/Factory.js", "../../phaser3-rex-plugins/templates/ui/imageinputlabel/Factory.js", "../../phaser3-rex-plugins/templates/ui/statesimage/Factory.js", "../../phaser3-rex-plugins/templates/ui/statesroundrectangle/Factory.js", "../../phaser3-rex-plugins/templates/ui/statesnineslice/Factory.js", "../../phaser3-rex-plugins/templates/ui/statesninepatch/Factory.js", "../../phaser3-rex-plugins/templates/ui/statestext/Factory.js", "../../phaser3-rex-plugins/templates/ui/statesbitmaptext/Factory.js", "../../phaser3-rex-plugins/templates/ui/statesbarrectangle/Factory.js", "../../phaser3-rex-plugins/templates/ui/chart/Factory.js", "../../phaser3-rex-plugins/templates/ui/container/Factory.js", "../../phaser3-rex-plugins/templates/ui/sizer/Factory.js", "../../phaser3-rex-plugins/templates/ui/gridsizer/Factory.js", "../../phaser3-rex-plugins/templates/ui/fixwidthsizer/Factory.js", "../../phaser3-rex-plugins/templates/ui/overlapsizer/Factory.js", "../../phaser3-rex-plugins/templates/ui/space/Factory.js", "../../phaser3-rex-plugins/templates/ui/label/Factory.js", "../../phaser3-rex-plugins/templates/ui/simplelabel/Factory.js", "../../phaser3-rex-plugins/templates/ui/titlelabel/Factory.js", "../../phaser3-rex-plugins/templates/ui/simpletitlelabel/Factory.js", "../../phaser3-rex-plugins/templates/ui/namevaluelabel/Factory.js", "../../phaser3-rex-plugins/templates/ui/expbar/Factory.js", "../../phaser3-rex-plugins/templates/ui/buttons/Factory.js", "../../phaser3-rex-plugins/templates/ui/gridbuttons/Factory.js", "../../phaser3-rex-plugins/templates/ui/fixwidthbuttons/Factory.js", "../../phaser3-rex-plugins/templates/ui/fileselectorbutton/Factory.js", "../../phaser3-rex-plugins/templates/ui/dialog/Factory.js", "../../phaser3-rex-plugins/templates/ui/confirmdialog/Factory.js", "../../phaser3-rex-plugins/templates/ui/confirmactionbutton/Factory.js", "../../phaser3-rex-plugins/templates/ui/nameinputdialog/Factory.js", "../../phaser3-rex-plugins/templates/ui/holygrail/Factory.js", "../../phaser3-rex-plugins/templates/ui/tabs/Factory.js", "../../phaser3-rex-plugins/templates/ui/slider/Factory.js", "../../phaser3-rex-plugins/templates/ui/gridtable/Factory.js", "../../phaser3-rex-plugins/templates/ui/menu/Factory.js", "../../phaser3-rex-plugins/templates/ui/dropdownlist/Factory.js", "../../phaser3-rex-plugins/templates/ui/simpledropdownlist/Factory.js", "../../phaser3-rex-plugins/templates/ui/textbox/Factory.js", "../../phaser3-rex-plugins/templates/ui/simpletextbox/Factory.js", "../../phaser3-rex-plugins/templates/ui/numberbar/Factory.js", "../../phaser3-rex-plugins/templates/ui/scrollbar/Factory.js", "../../phaser3-rex-plugins/templates/ui/badgelabel/Factory.js", "../../phaser3-rex-plugins/templates/ui/pages/Factory.js", "../../phaser3-rex-plugins/templates/ui/perspectivecard/Factory.js", "../../phaser3-rex-plugins/templates/ui/tabpages/Factory.js", "../../phaser3-rex-plugins/templates/ui/folder/Factory.js", "../../phaser3-rex-plugins/templates/ui/trees/Factory.js", "../../phaser3-rex-plugins/templates/ui/textarea/Factory.js", "../../phaser3-rex-plugins/templates/ui/textareainput/Factory.js", "../../phaser3-rex-plugins/templates/ui/scrollablepanel/Factory.js", "../../phaser3-rex-plugins/templates/ui/toast/Factory.js", "../../phaser3-rex-plugins/templates/ui/toastqueue/Factory.js", "../../phaser3-rex-plugins/templates/ui/colorinput/colorinput/Factory.js", "../../phaser3-rex-plugins/templates/ui/colorinput/colorinputbase/Factory.js", "../../phaser3-rex-plugins/templates/ui/colorinput/colorpicker/Factory.js", "../../phaser3-rex-plugins/templates/ui/colorinput/colorcomponents/Factory.js", "../../phaser3-rex-plugins/templates/ui/splitpanels/Factory.js", "../../phaser3-rex-plugins/templates/ui/sides/Factory.js", "../../phaser3-rex-plugins/templates/ui/tweaker/Factory.js", "../../phaser3-rex-plugins/templates/ui/click/Factory.js", "../../phaser3-rex-plugins/templates/ui/clickoutside/Factory.js", "../../phaser3-rex-plugins/templates/ui/intouching/Factory.js", "../../phaser3-rex-plugins/templates/ui/tap/Factory.js", "../../phaser3-rex-plugins/templates/ui/press/Factory.js", "../../phaser3-rex-plugins/templates/ui/swipe/Factory.js", "../../phaser3-rex-plugins/templates/ui/pan/Factory.js", "../../phaser3-rex-plugins/templates/ui/drag/Factory.js", "../../phaser3-rex-plugins/templates/ui/pinch/Factory.js", "../../phaser3-rex-plugins/templates/ui/rotate/Factory.js", "../../phaser3-rex-plugins/templates/ui/flip/Factory.js", "../../phaser3-rex-plugins/templates/ui/shake/Factory.js", "../../phaser3-rex-plugins/templates/ui/toucheventstop/Factory.js", "../../phaser3-rex-plugins/templates/ui/perspective/Factory.js", "../../phaser3-rex-plugins/templates/ui/skew/Factory.js", "../../phaser3-rex-plugins/templates/ui/anchor/Factory.js", "../../phaser3-rex-plugins/templates/ui/texttyping/Factory.js", "../../phaser3-rex-plugins/templates/ui/textpage/Factory.js", "../../phaser3-rex-plugins/templates/ui/textedit/Factory.js", "../../phaser3-rex-plugins/templates/ui/layermanager/Factory.js", "../../phaser3-rex-plugins/templates/ui/ui-plugin.js"], "sourcesContent": ["class ObjectFactory {\r\n    constructor(scene) {\r\n        this.scene = scene;\r\n\r\n        scene.events.once('destroy', this.destroy, this);\r\n    }\r\n\r\n    destroy() {\r\n        this.scene = null;\r\n    }\r\n\r\n    static register(type, callback) {\r\n        ObjectFactory.prototype[type] = callback;\r\n    }\r\n};\r\nexport default ObjectFactory;", "import NinePatch from './NinePatch.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('ninePatch', function (x, y, width, height, key, columns, rows, config) {\r\n    var gameObject = new NinePatch(this.scene, x, y, width, height, key, columns, rows, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.NinePatch', NinePatch);\r\n\r\nexport default NinePatch;", "import NinePatch from './NinePatch.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('ninePatch2', function (x, y, width, height, key, columns, rows, config) {\r\n    var gameObject = new NinePatch(this.scene, x, y, width, height, key, columns, rows, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.NinePatch2', NinePatch);\r\n\r\nexport default NinePatch;", "import RoundRectangle from './RoundRectangle.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('roundRectangle', function (x, y, width, height, radiusConfig, fillColor, fillAlpha) {\r\n    var gameObject = new RoundRectangle(this.scene, x, y, width, height, radiusConfig, fillColor, fillAlpha);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.RoundRectangle', RoundRectangle);\r\n\r\nexport default RoundRectangle;", "import RoundRectangleCanvas from './RoundRectangleCanvas.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('roundRectangleCanvas', function (x, y, width, height, radius, fillStyle, strokeStyle, lineWidth, fillColor2, isHorizontalGradient) {\r\n    var gameObject = new RoundRectangleCanvas(this.scene, x, y, width, height, radius, fillStyle, strokeStyle, lineWidth, fillColor2, isHorizontalGradient);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.RoundRectangleCanvas', RoundRectangleCanvas);\r\n\r\nexport default RoundRectangleCanvas;", "import QuadShape from './QuadShape.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('QuadShape', function (x, y, width, height, fillColor, fillAlpha) {\r\n    var gameObject = new QuadShape(this.scene, x, y, width, height, fillColor, fillAlpha);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.QuadShape', QuadShape);\r\n\r\nexport default QuadShape;", "import BBCodeText from './BBCodeText.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('BBCodeText', function (x, y, text, style) {\r\n    var gameObject = new BBCodeText(this.scene, x, y, text, style);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.BBCodeText', BBCodeText);\r\n\r\nexport default BBCodeText;", "import TagText from './TagText.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('tagText', function (x, y, text, style) {\r\n    var gameObject = new TagText(this.scene, x, y, text, style);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TagText', TagText);\r\n\r\nexport default TagText;", "import DynamicText from './DynamicText.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('dynamicText', function (x, y, width, height, config) {\r\n    var gameObject = new DynamicText(this.scene, x, y, width, height, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.DynamicText', DynamicText);\r\n\r\nexport default DynamicText;", "import TextPlayer from './TextPlayer.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textPlayer', function (x, y, width, height, config) {\r\n    var gameObject = new TextPlayer(this.scene, x, y, width, height, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextPlayer', TextPlayer);\r\n\r\nexport default TextPlayer;", "import CanvasInput from './CanvasInput.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('canvasInput', function (x, y, fixedWidth, fixedHeight, config) {\r\n    var gameObject = new CanvasInput(this.scene, x, y, fixedWidth, fixedHeight, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.CanvasInput', CanvasInput);\r\n\r\nexport default CanvasInput;", "import HiddenEdit from './HiddenEdit.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('hiddenEdit', function (textObject, config) {\r\n    var gameObject = new HiddenEdit(textObject, config);\r\n    // Note: Don't add this game object into scene\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.HiddenEdit', HiddenEdit);\r\n\r\nexport default HiddenEdit;", "import Checkbox from './Checkbox.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('checkbox', function (x, y, width, height, color, config) {\r\n    var gameObject = new Checkbox(this.scene, x, y, width, height, color, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Checkbox', Checkbox);\r\n\r\nexport default Checkbox;", "import ToggleSwitch from './ToggleSwitch.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('toggleSwitch', function (x, y, width, height, color, config) {\r\n    var gameObject = new ToggleSwitch(this.scene, x, y, width, height, color, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ToggleSwitch', ToggleSwitch);\r\n\r\nexport default ToggleSwitch;", "import Canvas from './Canvas.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('canvas', function (x, y, width, height) {\r\n    var gameObject = new Canvas(this.scene, x, y, width, height);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Canvas', Canvas);\r\n\r\nexport default Canvas;", "import CircleMaskImage from './CircleMaskImage.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('circleMaskImage', function (x, y, key, frame, config) {\r\n    var gameObject = new CircleMaskImage(this.scene, x, y, key, frame, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.CircleMaskImage', CircleMaskImage);\r\n\r\nexport default CircleMaskImage;", "import AlphaMaskImage from './AlphaMaskImage.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('alphaMaskImage', function (x, y, key, frame, config) {\r\n    var gameObject = new AlphaMaskImage(this.scene, x, y, key, frame, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.AlphaMaskImage', AlphaMaskImage);\r\n\r\nexport default AlphaMaskImage;", "import CircularProgress from './CircularProgress.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('circularProgress', function (x, y, radius, barColor, value, config) {\r\n    var gameObject = new CircularProgress(this.scene, x, y, radius, barColor, value, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.CircularProgress', CircularProgress);\r\n\r\nexport default CircularProgress;", "import CircularProgressCanvas from './CircularProgressCanvas.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('circularProgressCanvas', function (x, y, radius, barColor, value, config) {\r\n    var gameObject = new CircularProgressCanvas(this.scene, x, y, radius, barColor, value, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.CircularProgressCanvas', CircularProgressCanvas);\r\n\r\nexport default CircularProgressCanvas;", "import LineProgress from './LineProgress.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('lineProgress', function (x, y, width, height, barColor, value, config) {\r\n    var gameObject = new LineProgress(this.scene, x, y, width, height, barColor, value, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.LineProgress', LineProgress);\r\n\r\nexport default LineProgress;", "import RoundRectangleProgress from './RoundRectangleProgress.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('roundRectanleProgress', function (x, y, width, height, radiusConfig, barColor, value, config) {\r\n    var gameObject = new RoundRectangleProgress(this.scene, x, y, width, height, radiusConfig, barColor, value, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.RoundRectangleProgress', RoundRectangleProgress);\r\n\r\nexport default RoundRectangleProgress;", "import LineProgressCanvas from './LineProgressCanvas.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('circularProgressCanvas', function (x, y, width, height, barColor, value, config) {\r\n    var gameObject = new LineProgressCanvas(this.scene, x, y, width, height, barColor, value, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.LineProgressCanvas', LineProgressCanvas);\r\n\r\nexport default LineProgressCanvas;", "import Triangle from './Triangle.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('triangle', function (x, y, width, height, fillColor, fillAlpha) {\r\n    var gameObject = new Triangle(this.scene, x, y, width, height, fillColor, fillAlpha);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Triangle', Triangle);\r\n\r\nexport default Triangle;", "import Knob from './Knob.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('knob', function (config) {\r\n    var gameObject = new Knob(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Knob', Knob);\r\n\r\nexport default Knob;", "import CustomShapes from './CustomShapes.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('customShapes', function (x, y, width, height, config) {\r\n    var gameObject = new CustomShapes(this.scene, x, y, width, height, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.CustomShapes', CustomShapes);\r\n\r\nexport default CustomShapes;", "import CustomProgress from './CustomProgress.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('customProgress', function (x, y, width, height, config) {\r\n    var gameObject = new CustomProgress(this.scene, x, y, width, height, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.CustomProgress', CustomProgress);\r\n\r\nexport default CustomProgress;", "import AIOSpinner from './AIOSpinner.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('aioSpinner', function (config) {\r\n    var gameObject = new AIOSpinner(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.AIOSpinner', AIOSpinner);\r\n\r\nexport default AIOSpinner;", "import TransitionImage from './TransitionImage.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('transitionImage', function (x, y, texture, frame, config) {\r\n    var gameObject = new TransitionImage(this.scene, x, y, texture, frame, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TransitionImage', TransitionImage);\r\n\r\nexport default TransitionImage;", "import TransitionImagePack from './TransitionImagePack.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('transitionImagePack', function (x, y, texture, frame, config) {\r\n    var gameObject = new TransitionImagePack(this.scene, x, y, texture, frame, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TransitionImagePack', TransitionImagePack);\r\n\r\nexport default TransitionImagePack;", "import ImageBox from './ImageBox.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('imageBox', function (x, y, texture, frame, config) {\r\n    var gameObject = new ImageBox(this.scene, x, y, texture, frame, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ImageBox', ImageBox);\r\n\r\nexport default ImageBox;", "import FullWindowRectangle from './FullWindowRectangle.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fullWindowRectangle', function (fillColor, fillAlpha) {\r\n    var gameObject = new FullWindowRectangle(this.scene, fillColor, fillAlpha);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FullWindowRectangle', FullWindowRectangle);\r\n\r\nexport default FullWindowRectangle;", "import FullWindowZone from './FullWindowZone.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fullWindowZone', function () {\r\n    var gameObject = new FullWindowZone(this.scene);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FullWindowZone', FullWindowZone);\r\n\r\nexport default FullWindowZone;", "import Cover from './Cover.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('cover', function (config) {\r\n    var gameObject = new Cover(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Cover', Cover);\r\n\r\nexport default Cover;", "import InputText from './InputText.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('inputText', function (config) {\r\n    var gameObject = new InputText(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.InputText', InputText);\r\n\r\nexport default InputText;", "import { FileChooser } from './FileChooser.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fileChooser', function (config) {\r\n    var gameObject = new FileChooser(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FileChooser', FileChooser);\r\n\r\nexport default FileChooser;", "import FileDropZone from './FileDropZone.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fileDropZone', function (config) {\r\n    var gameObject = new FileDropZone(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FileDropZone', FileDropZone);\r\n\r\nexport default FileDropZone;", "import ImageInputLabel from './ImageInputLabel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('imageInputLabel', function (config) {\r\n    var gameObject = new ImageInputLabel(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ImageInputLabel', ImageInputLabel);\r\n\r\nexport default ImageInputLabel;", "import StatesImage from './StatesImage.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesImage', function (config) {\r\n    var gameObject = new StatesImage(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesImage', StatesImage);\r\n\r\nexport default StatesImage;", "import StatesRoundRectangle from './StatesRoundRectangle.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesRoundRectangle', function (config) {\r\n    var gameObject = new StatesRoundRectangle(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesRoundRectangle', StatesRoundRectangle);\r\n\r\nexport default StatesRoundRectangle;", "import StatesNineSlice from './StatesNineSlice.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesNineSlice', function (config) {\r\n    var gameObject = new StatesNineSlice(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesNineSlice', StatesNineSlice);\r\n\r\nexport default StatesNineSlice;", "import StatesNinePatch from './StatesNinePatch.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesNinePatch', function (config) {\r\n    var gameObject = new StatesNinePatch(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesNinePatch', StatesNinePatch);\r\n\r\nexport default StatesNinePatch;", "import StatesText from './StatesText.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesText', function (config) {\r\n    var gameObject = new StatesText(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesText', StatesText);\r\n\r\nexport default StatesText;", "import StatesBitmapText from './StatesBitmapText.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesBitmapText', function (config) {\r\n    var gameObject = new StatesBitmapText(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesBitmapText', StatesBitmapText);\r\n\r\nexport default StatesBitmapText;", "import StatesBarRectangle from './StatesBarRectangle.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('statesBarRectangle', function (config) {\r\n    var gameObject = new StatesBarRectangle(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.StatesBarRectangle', StatesBarRectangle);\r\n\r\nexport default StatesBarRectangle;", "import Chart from './Chart.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('chart', function (x, y, width, height, config) {\r\n    var gameObject = new Chart(this.scene, x, y, width, height, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Chart', Chart);\r\n\r\nexport default Chart;", "import Container from './Container.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('container', function (x, y, width, height, children) {\r\n    var gameObject = new Container(this.scene, x, y, width, height, children);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Container', Container);\r\n\r\nexport default Container;", "import Sizer from './Sizer.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('sizer', function (x, y, minWidth, minHeight, orientation, config) {\r\n    var gameObject = new Sizer(this.scene, x, y, minWidth, minHeight, orientation, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Sizer', Sizer);\r\n\r\nexport default Sizer;", "import GridSizer from './GridSizer.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('gridSizer', function (x, y, minWidth, minHeight, columnCount, rowCount, columnProportions, rowProportion, config) {\r\n    var gameObject = new GridSizer(this.scene, x, y, minWidth, minHeight, columnCount, rowCount, columnProportions, rowProportion, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.GridSizer', GridSizer);\r\n\r\nexport default GridSizer;", "import FixWidthSizer from './FixWidthSizer.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fixWidthSizer', function (x, y, minWidth, minHeight, config) {\r\n    var gameObject = new FixWidthSizer(this.scene, x, y, minWidth, minHeight, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FixWidthSizer', FixWidthSizer);\r\n\r\nexport default FixWidthSizer;", "import OverlapSizer from './OverlapSizer.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('overlapSizer', function (x, y, minWidth, minHeight, config) {\r\n    var gameObject = new OverlapSizer(this.scene, x, y, minWidth, minHeight, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.OverlapSizer', OverlapSizer);\r\n\r\nexport default OverlapSizer;", "import Space from './Space.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('space', function () {\r\n    var gameObject = new Space(this.scene);\r\n    // Don't add Zone into scene\r\n    // this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Space', Space);\r\n\r\nexport default Space;", "import Label from './Label.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('label', function (config) {\r\n    var gameObject = new Label(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Label', Label);\r\n\r\nexport default Label;", "import SimpleLabel from './SimpleLabel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('simpleLabel', function (config, creators) {\r\n    var gameObject = new SimpleLabel(this.scene, config, creators);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.SimpleLabel', SimpleLabel);\r\n\r\nexport default SimpleLabel;", "import TitleLabel from './TitleLabel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('titleLabel', function (config) {\r\n    var gameObject = new TitleLabel(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TitleLabel', TitleLabel);\r\n\r\nexport default TitleLabel;", "import SimpleTitleLabel from './SimpleTitleLabel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('simpleTitleLabel', function (config, creators) {\r\n    var gameObject = new SimpleTitleLabel(this.scene, config, creators);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.SimpleTitleLabel', SimpleTitleLabel);\r\n\r\nexport default SimpleTitleLabel;", "import NameValueLabel from './NameValueLabel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('nameValueLabel', function (config) {\r\n    var gameObject = new NameValueLabel(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.NameValueLabel', NameValueLabel);\r\n\r\nexport default NameValueLabel;", "import ExpBar from './ExpBar.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('expBar', function (config) {\r\n    var gameObject = new ExpBar(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ExpBar', ExpBar);\r\n\r\nexport default ExpBar;", "import Buttons from './Buttons.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('buttons', function (config) {\r\n    var gameObject = new Buttons(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Buttons', Buttons);\r\n\r\nexport default Buttons;", "import GridButtons from './GridButtons.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('gridButtons', function (config) {\r\n    var gameObject = new GridButtons(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.GridButtons', GridButtons);\r\n\r\nexport default GridButtons;", "import FixWidthButtons from './FixWidthButtons.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fixWidthButtons', function (config) {\r\n    var gameObject = new FixWidthButtons(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FixWidthButtons', FixWidthButtons);\r\n\r\nexport default FixWidthButtons;", "import FileSelectorButton from './FileSelectorButton.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('fileSelectorButton', function (config) {\r\n    var gameObject = new FileSelectorButton(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.FileSelectorButton', FileSelectorButton);\r\n\r\nexport default FileSelectorButton;", "import Dialog from './Dialog.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('dialog', function (config) {\r\n    var gameObject = new Dialog(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Dialog', Dialog);\r\n\r\nexport default Dialog;", "import ConfirmDialog from './ConfirmDialog.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('confirmDialog', function (config, creators) {\r\n    var gameObject = new ConfirmDialog(this.scene, config, creators);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ConfirmDialog', ConfirmDialog);\r\n\r\nexport default ConfirmDialog;", "import ConfirmActionButton from './ConfirmActionButton.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('confirmActionButton', function (config) {\r\n    var gameObject = new ConfirmActionButton(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ConfirmActionButton', ConfirmActionButton);\r\n\r\nexport default ConfirmActionButton;", "import NameInputDialog from './NameInputDialog.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('nameInputDialog', function (config, creators) {\r\n    var gameObject = new NameInputDialog(this.scene, config, creators);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.NameInputDialog', NameInputDialog);\r\n\r\nexport default NameInputDialog;", "import HolyGrail from './HolyGrail.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('holyGrail', function (config) {\r\n    var gameObject = new HolyGrail(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.HolyGrail', HolyGrail);\r\n\r\nexport default HolyGrail;", "import Tabs from './Tabs.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('tabs', function (config) {\r\n    var gameObject = new Tabs(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Tabs', Tabs);\r\n\r\nexport default Tabs;", "import Slider from './Slider.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('slider', function (config) {\r\n    var gameObject = new Slider(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Slider', Slider);\r\n\r\nexport default Slider;", "import GridTable from './GridTable.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('gridTable', function (config) {\r\n    var gameObject = new GridTable(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.GridTable', GridTable);\r\n\r\nexport default GridTable;", "import Menu from './Menu.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('menu', function (config) {\r\n    var gameObject = new Menu(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Menu', Menu);\r\n\r\nexport default Menu;", "import DropDownList from './DropDownList.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('dropDownList', function (config) {\r\n    var gameObject = new DropDownList(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.DropDownList', DropDownList);\r\n\r\nexport default DropDownList;", "import SimpleDropDownList from './SimpleDropDownList.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('simpleDropDownList', function (config, creators) {\r\n    var gameObject = new SimpleDropDownList(this.scene, config, creators);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.SimpleDropDownList', SimpleDropDownList);\r\n\r\nexport default SimpleDropDownList;", "import TextBox from './TextBox.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textBox', function (config) {\r\n    var gameObject = new TextBox(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextBox', TextBox);\r\n\r\nexport default TextBox;", "import SimpleTextBox from './SimpleTextBox.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('simpleTextBox', function (config) {\r\n    var gameObject = new SimpleTextBox(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.SimpleTextBox', SimpleTextBox);\r\n\r\nexport default SimpleTextBox;", "import NumberBar from './NumberBar.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('numberBar', function (config) {\r\n    var gameObject = new NumberBar(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.NumberBar', NumberBar);\r\n\r\nexport default NumberBar;", "import ScrollBar from './ScrollBar.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('scrollBar', function (config) {\r\n    var gameObject = new ScrollBar(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ScrollBar', ScrollBar);\r\n\r\nexport default ScrollBar;", "import BadgeLabel from './BadgeLabel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('badgeLabel', function (config) {\r\n    var gameObject = new BadgeLabel(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.BadgeLabel', BadgeLabel);\r\n\r\nexport default BadgeLabel;", "import Pages from './Pages.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('pages', function (config) {\r\n    var gameObject = new Pages(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Pages', Pages);\r\n\r\nexport default Pages;", "import PerspectiveCard from './PerspectiveCard.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('perspectiveCard', function (config) {\r\n    var gameObject = new PerspectiveCard(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.PerspectiveCard', PerspectiveCard);\r\n\r\nexport default PerspectiveCard;", "import TabPages from './TabPages.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('tabPages', function (config) {\r\n    var gameObject = new TabPages(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TabPages', TabPages);\r\n\r\nexport default TabPages;", "import Folder from './Folder.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('folder', function (config) {\r\n    var gameObject = new Folder(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Folder', Folder);\r\n\r\nexport default Folder;", "import Trees from './Trees.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('trees', function (config) {\r\n    var gameObject = new Trees(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Trees', Trees);\r\n\r\nexport default Trees;", "import TextArea from './TextArea.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textArea', function (config) {\r\n    var gameObject = new TextArea(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextArea', TextArea);\r\n\r\nexport default TextArea;", "import TextAreaInput from './TextAreaInput.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textAreaInput', function (config) {\r\n    var gameObject = new TextAreaInput(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextAreaInput', TextAreaInput);\r\n\r\nexport default TextAreaInput;", "import ScrollablePanel from './ScrollablePanel.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('scrollablePanel', function (config) {\r\n    var gameObject = new ScrollablePanel(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ScrollablePanel', ScrollablePanel);\r\n\r\nexport default ScrollablePanel;", "import Toast from './Toast.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('toast', function (config) {\r\n    var gameObject = new Toast(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Toast', Toast);\r\n\r\nexport default Toast;", "import ToastQueue from './ToastQueue.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('toastQueue', function (config) {\r\n    var gameObject = new ToastQueue(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ToastQueue', ToastQueue);\r\n\r\nexport default ToastQueue;", "import ColorInput from './ColorInput.js';\r\nimport ObjectFactory from '../../ObjectFactory.js';\r\nimport SetValue from '../../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('colorInput', function (config) {\r\n    var gameObject = new ColorInput(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ColorInput', ColorInput);\r\n\r\nexport default ColorInput;", "import ColorInputBase from './ColorInputBase.js';\r\nimport ObjectFactory from '../../ObjectFactory.js';\r\nimport SetValue from '../../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('colorInputLite', function (config) {\r\n    var gameObject = new ColorInputBase(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ColorInputBase', ColorInputBase);\r\n\r\nexport default ColorInputBase;", "import ColorPicker from './ColorPicker.js';\r\nimport ObjectFactory from '../../ObjectFactory.js';\r\nimport SetValue from '../../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('colorPicker', function (config) {\r\n    var gameObject = new ColorPicker(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ColorPicker', ColorPicker);\r\n\r\nexport default ColorPicker;", "import ColorComponents from './ColorComponents.js';\r\nimport ObjectFactory from '../../ObjectFactory.js';\r\nimport SetValue from '../../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('colorComponents', function (config) {\r\n    var gameObject = new ColorComponents(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ColorComponents', ColorComponents);\r\n\r\nexport default ColorComponents;", "import SplitPanels from './SplitPanels.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('splitPanels', function (config) {\r\n    var gameObject = new SplitPanels(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.SplitPanels', SplitPanels);\r\n\r\nexport default SplitPanels;", "import Sides from './Sides.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('sides', function (config) {\r\n    var gameObject = new Sides(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Sides', Sides);\r\n\r\nexport default Sides;", "import Tweaker from './Tweaker.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('tweaker', function (config) {\r\n    var gameObject = new Tweaker(this.scene, config);\r\n    this.scene.add.existing(gameObject);\r\n    return gameObject;\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Tweaker', Tweaker);\r\n\r\nexport default Tweaker;", "import Click from './Click.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('click', function (gameObject, config) {\r\n    return new Click(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Click', Click);\r\n\r\nexport default Click;", "import ClickOutside from './ClickOutside.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('clickOutside', function (gameObject, config) {\r\n    return new ClickOutside(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.ClickOutside', ClickOutside);\r\n\r\nexport default ClickOutside;", "import InTouching from './InTouching.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('inTouching', function (gameObject, config) {\r\n    return new InTouching(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.InTouching', InTouching);\r\n\r\nexport default InTouching;", "import Tap from './Tap.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport IsGameObject from '../../../plugins/utils/system/IsGameObject.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('tap', function (gameObject, config) {\r\n    if (!IsGameObject(gameObject)) {\r\n        config = gameObject;\r\n        gameObject = this.scene;\r\n    }\r\n    return new Tap(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Tap', Tap);\r\n\r\nexport default Tap;", "import Press from './Press.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport IsGameObject from '../../../plugins/utils/system/IsGameObject.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('press', function (gameObject, config) {\r\n    if (!IsGameObject(gameObject)) {\r\n        config = gameObject;\r\n        gameObject = this.scene;\r\n    }\r\n    return new Press(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Press', Press);\r\n\r\nexport default Press;", "import Swipe from './Swipe.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport IsGameObject from '../../../plugins/utils/system/IsGameObject.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('swipe', function (gameObject, config) {\r\n    if (!IsGameObject(gameObject)) {\r\n        config = gameObject;\r\n        gameObject = this.scene;\r\n    }\r\n    return new Swipe(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Swipe', Swipe);\r\n\r\nexport default Swipe;", "import Pan from './Pan.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport IsGameObject from '../../../plugins/utils/system/IsGameObject.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('pan', function (gameObject, config) {\r\n    if (!IsGameObject(gameObject)) {\r\n        config = gameObject;\r\n        gameObject = this.scene;\r\n    }\r\n    return new Pan(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Pan', Pan);\r\n\r\nexport default Pan;", "import Drag from './Drag.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('drag', function (gameObject, config) {\r\n    return new Drag(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Drag', Drag);\r\n\r\nexport default Drag;", "import Pinch from './Pinch.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('pinch', function (config) {\r\n    return new Pinch(this.scene, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Pinch', Pinch);\r\n\r\nexport default Pinch;", "import Rotate from './Rotate.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('rotate', function (config) {\r\n    return new Rotate(this.scene, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Rotate', Rotate);\r\n\r\nexport default Rotate;", "import Flip from './Flip.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('flip', function (gameObject, config) {\r\n    return new Flip(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Flip', Flip);\r\n\r\nexport default Flip;", "import Shake from './Shake.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('shake', function (gameObject, config) {\r\n    return new Shake(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Shake', Shake);\r\n\r\nexport default Shake;", "import TouchEventStop from './TouchEventStop.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('touchEventStop', function (gameObject, config) {\r\n    return new TouchEventStop(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TouchEventStop', TouchEventStop);\r\n\r\nexport default TouchEventStop;", "import Perspective from './Perspective.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('perspective', function (gameObject, config) {\r\n    return new Perspective(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Perspective', Perspective);\r\n\r\nexport default Perspective;", "import Skew from './Skew.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('skew', function (gameObject, config) {\r\n    return new Skew(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Skew', Skew);\r\n\r\nexport default Skew;", "import Anchor from \"./Anchor.js\";\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('anchor', function (gameObject, config) {\r\n    return new Anchor(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.Anchor', Anchor);\r\n\r\nexport default Anchor;", "import TextTyping from './TextTyping.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textTyping', function (gameObject, config) {\r\n    return new TextTyping(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextTyping', TextTyping);\r\n\r\nexport default TextTyping;", "import TextPage from './TextPage.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textPage', function (gameObject, config) {\r\n    return new TextPage(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextPage', TextPage);\r\n\r\nexport default TextPage;", "import TextEdit from './TextEdit.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('textEdit', function (gameObject, config) {\r\n    return new TextEdit(gameObject, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.TextEdit', TextEdit);\r\n\r\nexport default TextEdit;", "import LayerManager from './LayerManager.js';\r\nimport ObjectFactory from '../ObjectFactory.js';\r\nimport SetValue from '../../../plugins/utils/object/SetValue.js';\r\n\r\nObjectFactory.register('layerManager', function (config) {\r\n    return new LayerManager(this.scene, config);\r\n});\r\n\r\nSetValue(window, 'RexPlugins.UI.LayerManager', LayerManager);\r\n\r\nexport default LayerManager;", "import ObjectFactory from './ObjectFactory.js';\r\n\r\nimport NinePatchFactory from './ninepatch/Factory.js';\r\nimport NinePatch2Factory from './ninepatch2/Factory.js';\r\nimport RoundRectangleFactory from './roundrectangle/Factory.js';\r\nimport RoundRectangleCanvasFactory from './roundrectanglecanvas/Factory.js';\r\nimport QuadShapeFactory from './quadshape/Factory.js';\r\nimport BBCodeTextFactory from './bbcodetext/Factory.js';\r\nimport TagTextFactory from './tagtext/Factory.js';\r\nimport DynamicTextFactory from './dynamictext/Factory.js';\r\nimport TextPlayerFactory from './textplayer/Factory.js';\r\nimport CanvasInputFactory from './canvasinput/Factory.js';\r\nimport HiddenEditFactory from './hiddenedit/Factory.js';\r\nimport CheckboxFactory from './checkbox/Factory.js';\r\nimport ToggleSwitchFactory from './toggleswitch/Factory.js';\r\nimport CanvasFactory from './canvas/Factory.js';\r\nimport CircleMaskImageFactory from './circlemaskimage/Factory.js';\r\nimport AlphaMaskImageFactory from './alphamaskimage/Factory.js';\r\nimport CircularProgressFactory from './circularprogress/Factory.js';\r\nimport CircularProgressCanvasFactory from './circularprogresscanvas/Factory.js';\r\nimport LineProgressFactory from './lineprogress/Factory.js';\r\nimport RoundRectangleProgressFactory from './roundrectangleprogress/Factory.js';\r\nimport LineProgressCanvasFactory from './lineprogresscanvas/Factory.js';\r\nimport TriangleFactory from './triangle/Factory.js';\r\nimport KnobFactory from './knob/Factory.js';\r\nimport CustomShapesFactory from './customshapes/Factory.js';\r\nimport CustomProgressFactory from './customprogress/Factory.js';\r\nimport AIOSpinnerFactory from './aiospinner/Factory.js';\r\nimport TransitionImageFactory from './transitionimage/Factory.js';\r\nimport TransitionImagePackFactory from './transitionimagepack/Factory.js';\r\nimport ImageBoxFactory from './imagebox/Factory.js';\r\nimport FullWindowRectangleFactory from './fullwindowrectangle/Factory.js';\r\nimport FullWindowZoneFactory from './fullwindowzone/Factory.js';\r\nimport CoverFactory from './cover/Factory.js';\r\nimport InputTextFactory from './inputtext/Factory';\r\nimport FileChooserFactory from './filechooser/Factory.js';\r\nimport FileDropZoneFactory from './filedropzone/Factory.js';\r\nimport ImageInputLabelFactory from './imageinputlabel/Factory.js';\r\nimport StatesImageFactory from './statesimage/Factory.js';\r\nimport StatesRoundRectangleFactory from './statesroundrectangle/Factory.js';\r\nimport StatesNineSliceFactory from './statesnineslice/Factory.js';\r\nimport StatesNinePatchFactory from './statesninepatch/Factory.js';\r\nimport StatesTextFactory from './statestext/Factory.js';\r\nimport StatesBitmapTextFactory from './statesbitmaptext/Factory.js';\r\nimport StatesBarRectangleFactory from './statesbarrectangle/Factory.js';\r\nimport ChartFactory from './chart/Factory.js';\r\n\r\nimport ContainerFactory from './container/Factory.js';\r\nimport SizerFactory from './sizer/Factory.js';\r\nimport GridSizerFactory from './gridsizer/Factory.js';\r\nimport FixWidthSizerFactory from './fixwidthsizer/Factory.js';\r\nimport OverlapSizerFactory from './overlapsizer/Factory.js';\r\n\r\nimport SpaceFactory from './space/Factory.js';\r\nimport LabelFactory from './label/Factory.js';\r\nimport SimpleLabelFactory from './simplelabel/Factory.js';\r\nimport TitleLabelFactory from './titlelabel/Factory.js';\r\nimport SimpleTitleLabelFactory from './simpletitlelabel/Factory.js';\r\nimport NameValueLabelFactory from './namevaluelabel/Factory.js';\r\nimport ExpBarFactory from './expbar/Factory.js';\r\nimport ButtonsFactory from './buttons/Factory.js';\r\nimport GridButtonsFactory from './gridbuttons/Factory.js';\r\nimport FixWidthButtonsFactory from './fixwidthbuttons/Factory.js';\r\nimport FileSelectorButtonFactory from './fileselectorbutton/Factory.js';\r\nimport DialogFactory from './dialog/Factory.js';\r\nimport ConfirmDialogFactory from './confirmdialog/Factory.js';\r\nimport ConfirmActionButtonFactory from './confirmactionbutton/Factory.js';\r\nimport NameInputDialogFactory from './nameinputdialog/Factory.js';\r\nimport HolyGrailFactory from './holygrail/Factory.js';\r\nimport TabsFactory from './tabs/Factory.js';\r\nimport SliderFactory from './slider/Factory.js';\r\nimport GridTableFactory from './gridtable/Factory.js';\r\nimport MenuFactory from './menu/Factory.js';\r\nimport DropDownListFactory from './dropdownlist/Factory.js';\r\nimport SimpleDropDownListFactory from './simpledropdownlist/Factory.js';\r\nimport TextBoxFactory from './textbox/Factory.js';\r\nimport SimpleTextBoxFactory from './simpletextbox/Factory.js';\r\nimport NumberBarFactory from './numberbar/Factory.js';\r\nimport ScrollBarFactory from './scrollbar/Factory.js';\r\nimport BadgeLabelFactory from './badgelabel/Factory.js';\r\nimport PagesFactory from './pages/Factory.js';\r\nimport PerspectiveCardFactory from './perspectivecard/Factory.js';\r\nimport TabPagesFactory from './tabpages/Factory.js';\r\nimport FolderFactory from './folder/Factory.js';\r\nimport TreesFactory from './trees/Factory.js';\r\nimport TextAreaFactory from './textarea/Factory.js';\r\nimport TextAreaInputFactory from './textareainput/Factory.js';\r\nimport ScrollablePanelFactory from './scrollablepanel/Factory.js';\r\nimport ToastFactory from './toast/Factory.js';\r\nimport ToastQueueFactory from './toastqueue/Factory.js';\r\nimport ColorInputFactory from './colorinput/colorinput/Factory.js';\r\nimport ColorInputLiteFactory from './colorinput/colorinputbase/Factory.js';\r\nimport ColorPickerFactory from './colorinput/colorpicker/Factory.js';\r\nimport ColorComponentsFactory from './colorinput/colorcomponents/Factory.js';\r\nimport SplitPanelsFactory from './splitpanels/Factory.js';\r\nimport SidesFactory from './sides/Factory.js';\r\nimport TweakerFactory from './tweaker/Factory.js';\r\n\r\nimport ClickFactory from './click/Factory.js';\r\nimport ClickOutsideFactory from './clickoutside/Factory.js';\r\nimport InTouchingFactory from './intouching/Factory.js';\r\nimport TapFactory from './tap/Factory.js';\r\nimport PressFactory from './press/Factory.js';\r\nimport SwipeFactory from './swipe/Factory.js';\r\nimport PanFactory from './pan/Factory.js';\r\nimport DragFactory from './drag/Factory.js';\r\nimport PinchFactory from './pinch/Factory.js';\r\nimport RotateFactory from './rotate/Factory.js';\r\nimport FlipFactory from './flip/Factory.js';\r\nimport ShakeFactory from './shake/Factory.js';\r\nimport TouchEventStopFactory from './toucheventstop/Factory.js';\r\nimport PerspectiveFactory from './perspective/Factory.js';\r\nimport SkewFactory from './skew/Factory.js';\r\nimport AnchorFactory from './anchor/Factory.js';\r\nimport TextTypingFactory from './texttyping/Factory.js';\r\nimport TextPageFactory from './textpage/Factory.js';\r\nimport TextEditFactory from './textedit/Factory.js';\r\nimport LayerManagerFactory from './layermanager/Factory.js';\r\n\r\nimport { GetParentSizer, GetTopmostSizer } from './utils/GetParentSizer.js';\r\nimport RemoveFromParent from './utils/RemoveFromParent.js';\r\nimport IsPointerInBounds from '../../plugins/utils/input/IsPointerInBounds.js';\r\nimport { Show, Hide, IsShown, } from './utils/Hide.js';\r\nimport ConfirmAction from './confirmdialog/ConfirmAction.js';\r\nimport Edit from './textedit/Edit.js';\r\nimport WrapExpandText from './utils/wrapexpandtext/WrapExpandText.js';\r\nimport FontSizeExpandText from './utils/fontsizeexpandtext/FontSizeExpandText.js';\r\nimport SetFontSizeToFitWidth from '../../plugins/utils/text/fontsizefit/FontSizeFit.js';\r\nimport { WaitEvent, WaitComplete } from './utils/WaitEvent.js';\r\nimport Delay from '../../plugins/utils/promise/Delay.js';\r\nimport GetViewport from '../../plugins/utils/system/GetViewport.js';\r\nimport SetChildrenInteractive from './utils/setchildreninteractive/SetChildrenInteractive.js';\r\nimport { FadeIn, FadeOutDestroy } from './fade/Fade.js';\r\nimport { EaseMoveTo, EaseMoveFrom } from './easemove/EaseMove.js'\r\nimport { Modal, ModalPromise, ModalClose } from './modal/Modal.js';\r\nimport RequestDrag from '../../plugins/utils/input/RequestDrag.js';\r\nimport { OpenFileChooser } from './filechooser/FileChooser.js';\r\n\r\n\r\nclass UIPlugin extends Phaser.Plugins.ScenePlugin {\r\n    constructor(scene, pluginManager) {\r\n        super(scene, pluginManager);\r\n\r\n        this.add = new ObjectFactory(scene);\r\n    }\r\n\r\n    boot() {\r\n        var eventEmitter = this.scene.events;\r\n        eventEmitter.on('destroy', this.destroy, this);\r\n    }\r\n\r\n    destroy() {\r\n        this.add.destroy();\r\n        super.destroy();\r\n    }\r\n\r\n    isInTouching(gameObject, pointer, preTest, postTest) {\r\n        if (!gameObject.visible) {\r\n            return false;\r\n        }\r\n        return IsPointerInBounds(gameObject, pointer, preTest, postTest);\r\n    }\r\n\r\n    get viewport() {\r\n        return GetViewport(this.scene, this.scene.cameras.main, true);\r\n    }\r\n\r\n}\r\n\r\nvar methods = {\r\n    getParentSizer: GetParentSizer,\r\n    getTopmostSizer: GetTopmostSizer,\r\n    removeFromParent: RemoveFromParent,\r\n    hide: Hide,\r\n    show: Show,\r\n    isShown: IsShown,\r\n    confirmAction: ConfirmAction,\r\n    edit: Edit,\r\n    wrapExpandText: WrapExpandText,\r\n    fontSizeExpandText: FontSizeExpandText,\r\n    fontSizeResize: SetFontSizeToFitWidth,  // Backward compatibility\r\n    setFontSizeToFitWidth: SetFontSizeToFitWidth,\r\n    waitEvent: WaitEvent,\r\n    waitComplete: WaitComplete,\r\n    delayPromise: Delay,\r\n    setChildrenInteractive: SetChildrenInteractive,\r\n    fadeIn: FadeIn,\r\n    fadeOutDestroy: FadeOutDestroy,\r\n    easeMoveTo: EaseMoveTo,\r\n    easeMoveFrom: EaseMoveFrom,\r\n    modal: Modal,\r\n    modalPromise: ModalPromise,\r\n    modalClose: ModalClose,\r\n    requestDrag: RequestDrag,\r\n    openFileChooser: OpenFileChooser,\r\n}\r\n\r\nObject.assign(\r\n    UIPlugin.prototype,\r\n    methods\r\n);\r\n\r\n\r\nexport default UIPlugin;"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,gBAAN,MAAM,eAAc;AAAA,EAChB,YAAY,OAAO;AACf,SAAK,QAAQ;AAEb,UAAM,OAAO,KAAK,WAAW,KAAK,SAAS,IAAI;AAAA,EACnD;AAAA,EAEA,UAAU;AACN,SAAK,QAAQ;AAAA,EACjB;AAAA,EAEA,OAAO,SAAS,MAAM,UAAU;AAC5B,mBAAc,UAAU,IAAI,IAAI;AAAA,EACpC;AACJ;AACA,IAAO,wBAAQ;;;ACXf,sBAAc,SAAS,aAAa,SAAU,GAAG,GAAG,OAAO,QAAQ,KAAK,SAAS,MAAM,QAAQ;AAC3F,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,KAAK,SAAS,MAAM,MAAM;AAC1F,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,cAAc,SAAU,GAAG,GAAG,OAAO,QAAQ,KAAK,SAAS,MAAM,QAAQ;AAC5F,MAAI,aAAa,IAAIA,mBAAU,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,KAAK,SAAS,MAAM,MAAM;AAC1F,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4BA,kBAAS;;;ACNtD,sBAAc,SAAS,kBAAkB,SAAU,GAAG,GAAG,OAAO,QAAQ,cAAc,WAAW,WAAW;AACxG,MAAI,aAAa,IAAI,uBAAe,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,cAAc,WAAW,SAAS;AACvG,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACN/D,sBAAc,SAAS,wBAAwB,SAAU,GAAG,GAAG,OAAO,QAAQ,QAAQ,WAAW,aAAa,WAAW,YAAY,sBAAsB;AACvJ,MAAI,aAAa,IAAI,6BAAqB,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,QAAQ,WAAW,aAAa,WAAW,YAAY,oBAAoB;AACtJ,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,sCAAsC,4BAAoB;;;ACN3E,sBAAc,SAAS,aAAa,SAAU,GAAG,GAAG,OAAO,QAAQ,WAAW,WAAW;AACrF,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,WAAW,SAAS;AACpF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,cAAc,SAAU,GAAG,GAAG,MAAM,OAAO;AAC9D,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,GAAG,GAAG,MAAM,KAAK;AAC7D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,WAAW,SAAU,GAAG,GAAG,MAAM,OAAO;AAC3D,MAAI,aAAa,IAAI,gBAAQ,KAAK,OAAO,GAAG,GAAG,MAAM,KAAK;AAC1D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,yBAAyB,eAAO;;;ACNjD,sBAAc,SAAS,eAAe,SAAU,GAAG,GAAG,OAAO,QAAQ,QAAQ;AACzE,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,MAAM;AACxE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,cAAc,SAAU,GAAG,GAAG,OAAO,QAAQ,QAAQ;AACxE,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,MAAM;AACvE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,eAAe,SAAU,GAAG,GAAG,YAAY,aAAa,QAAQ;AACnF,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,GAAG,GAAG,YAAY,aAAa,MAAM;AAClF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,cAAc,SAAU,YAAY,QAAQ;AAC/D,MAAI,aAAa,IAAI,mBAAW,YAAY,MAAM;AAElD,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,YAAY,SAAU,GAAG,GAAG,OAAO,QAAQ,OAAO,QAAQ;AAC7E,MAAI,aAAa,IAAI,iBAAS,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,OAAO,MAAM;AAC5E,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACNnD,sBAAc,SAAS,gBAAgB,SAAU,GAAG,GAAG,OAAO,QAAQ,OAAO,QAAQ;AACjF,MAAI,aAAa,IAAI,qBAAa,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,OAAO,MAAM;AAChF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACN3D,sBAAc,SAAS,UAAU,SAAU,GAAG,GAAG,OAAO,QAAQ;AAC5D,MAAI,aAAa,IAAI,eAAO,KAAK,OAAO,GAAG,GAAG,OAAO,MAAM;AAC3D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACN/C,sBAAc,SAAS,mBAAmB,SAAU,GAAG,GAAG,KAAK,OAAO,QAAQ;AAC1E,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,GAAG,GAAG,KAAK,OAAO,MAAM;AACzE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,kBAAkB,SAAU,GAAG,GAAG,KAAK,OAAO,QAAQ;AACzE,MAAI,aAAa,IAAI,uBAAe,KAAK,OAAO,GAAG,GAAG,KAAK,OAAO,MAAM;AACxE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACN/D,sBAAc,SAAS,oBAAoB,SAAU,GAAG,GAAG,QAAQ,UAAU,OAAO,QAAQ;AACxF,MAAI,aAAa,IAAI,yBAAiB,KAAK,OAAO,GAAG,GAAG,QAAQ,UAAU,OAAO,MAAM;AACvF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,kCAAkC,wBAAgB;;;ACNnE,sBAAc,SAAS,0BAA0B,SAAU,GAAG,GAAG,QAAQ,UAAU,OAAO,QAAQ;AAC9F,MAAI,aAAa,IAAI,+BAAuB,KAAK,OAAO,GAAG,GAAG,QAAQ,UAAU,OAAO,MAAM;AAC7F,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wCAAwC,8BAAsB;;;ACN/E,sBAAc,SAAS,gBAAgB,SAAU,GAAG,GAAG,OAAO,QAAQ,UAAU,OAAO,QAAQ;AAC3F,MAAI,aAAa,IAAI,qBAAa,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,UAAU,OAAO,MAAM;AAC1F,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACN3D,sBAAc,SAAS,yBAAyB,SAAU,GAAG,GAAG,OAAO,QAAQ,cAAc,UAAU,OAAO,QAAQ;AAClH,MAAI,aAAa,IAAI,+BAAuB,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,cAAc,UAAU,OAAO,MAAM;AAClH,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wCAAwC,8BAAsB;;;ACN/E,sBAAc,SAAS,0BAA0B,SAAU,GAAG,GAAG,OAAO,QAAQ,UAAU,OAAO,QAAQ;AACrG,MAAI,aAAa,IAAI,2BAAmB,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,UAAU,OAAO,MAAM;AAChG,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,oCAAoC,0BAAkB;;;ACNvE,sBAAc,SAAS,YAAY,SAAU,GAAG,GAAG,OAAO,QAAQ,WAAW,WAAW;AACpF,MAAI,aAAa,IAAI,iBAAS,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,WAAW,SAAS;AACnF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACNnD,sBAAc,SAAS,QAAQ,SAAU,QAAQ;AAC7C,MAAI,aAAa,IAAI,aAAK,KAAK,OAAO,MAAM;AAC5C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,sBAAsB,YAAI;;;ACN3C,sBAAc,SAAS,gBAAgB,SAAU,GAAG,GAAG,OAAO,QAAQ,QAAQ;AAC1E,MAAI,aAAa,IAAI,qBAAa,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,MAAM;AACzE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACN3D,sBAAc,SAAS,kBAAkB,SAAU,GAAG,GAAG,OAAO,QAAQ,QAAQ;AAC5E,MAAI,aAAa,IAAI,uBAAe,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,MAAM;AAC3E,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACN/D,sBAAc,SAAS,cAAc,SAAU,QAAQ;AACnD,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,MAAM;AAClD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,mBAAmB,SAAU,GAAG,GAAG,SAAS,OAAO,QAAQ;AAC9E,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,GAAG,GAAG,SAAS,OAAO,MAAM;AAC7E,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,uBAAuB,SAAU,GAAG,GAAG,SAAS,OAAO,QAAQ;AAClF,MAAI,aAAa,IAAI,4BAAoB,KAAK,OAAO,GAAG,GAAG,SAAS,OAAO,MAAM;AACjF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,qCAAqC,2BAAmB;;;ACNzE,sBAAc,SAAS,YAAY,SAAU,GAAG,GAAG,SAAS,OAAO,QAAQ;AACvE,MAAI,aAAa,IAAI,iBAAS,KAAK,OAAO,GAAG,GAAG,SAAS,OAAO,MAAM;AACtE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACNnD,sBAAc,SAAS,uBAAuB,SAAU,WAAW,WAAW;AAC1E,MAAI,aAAa,IAAI,4BAAoB,KAAK,OAAO,WAAW,SAAS;AACzE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,qCAAqC,2BAAmB;;;ACNzE,sBAAc,SAAS,kBAAkB,WAAY;AACjD,MAAI,aAAa,IAAI,uBAAe,KAAK,KAAK;AAC9C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACN/D,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,MAAM;AAC7C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,aAAa,SAAU,QAAQ;AAClD,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,MAAM;AACjD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,eAAe,SAAU,QAAQ;AACpD,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,MAAM;AACnD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,gBAAgB,SAAU,QAAQ;AACrD,MAAI,aAAa,IAAI,qBAAa,KAAK,OAAO,MAAM;AACpD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACN3D,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,eAAe,SAAU,QAAQ;AACpD,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,MAAM;AACnD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,wBAAwB,SAAU,QAAQ;AAC7D,MAAI,aAAa,IAAI,6BAAqB,KAAK,OAAO,MAAM;AAC5D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,sCAAsC,4BAAoB;;;ACN3E,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,cAAc,SAAU,QAAQ;AACnD,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,MAAM;AAClD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,oBAAoB,SAAU,QAAQ;AACzD,MAAI,aAAa,IAAI,yBAAiB,KAAK,OAAO,MAAM;AACxD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,kCAAkC,wBAAgB;;;ACNnE,sBAAc,SAAS,sBAAsB,SAAU,QAAQ;AAC3D,MAAI,aAAa,IAAI,2BAAmB,KAAK,OAAO,MAAM;AAC1D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,oCAAoC,0BAAkB;;;ACNvE,sBAAc,SAAS,SAAS,SAAU,GAAG,GAAG,OAAO,QAAQ,QAAQ;AACnE,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,MAAM;AAClE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,aAAa,SAAU,GAAG,GAAG,OAAO,QAAQ,UAAU;AACzE,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,GAAG,GAAG,OAAO,QAAQ,QAAQ;AACxE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,SAAS,SAAU,GAAG,GAAG,UAAU,WAAW,aAAa,QAAQ;AACtF,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,GAAG,GAAG,UAAU,WAAW,aAAa,MAAM;AACrF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,aAAa,SAAU,GAAG,GAAG,UAAU,WAAW,aAAa,UAAU,mBAAmB,eAAe,QAAQ;AACtI,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,GAAG,GAAG,UAAU,WAAW,aAAa,UAAU,mBAAmB,eAAe,MAAM;AACrI,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,iBAAiB,SAAU,GAAG,GAAG,UAAU,WAAW,QAAQ;AACjF,MAAI,aAAa,IAAI,sBAAc,KAAK,OAAO,GAAG,GAAG,UAAU,WAAW,MAAM;AAChF,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,+BAA+B,qBAAa;;;ACN7D,sBAAc,SAAS,gBAAgB,SAAU,GAAG,GAAG,UAAU,WAAW,QAAQ;AAChF,MAAI,aAAa,IAAI,qBAAa,KAAK,OAAO,GAAG,GAAG,UAAU,WAAW,MAAM;AAC/E,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACN3D,sBAAc,SAAS,SAAS,WAAY;AACxC,MAAI,aAAa,IAAI,cAAM,KAAK,KAAK;AAGrC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACP7C,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,MAAM;AAC7C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,eAAe,SAAU,QAAQ,UAAU;AAC9D,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,QAAQ,QAAQ;AAC7D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,cAAc,SAAU,QAAQ;AACnD,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,MAAM;AAClD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,oBAAoB,SAAU,QAAQ,UAAU;AACnE,MAAI,aAAa,IAAI,yBAAiB,KAAK,OAAO,QAAQ,QAAQ;AAClE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,kCAAkC,wBAAgB;;;ACNnE,sBAAc,SAAS,kBAAkB,SAAU,QAAQ;AACvD,MAAI,aAAa,IAAI,uBAAe,KAAK,OAAO,MAAM;AACtD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACN/D,sBAAc,SAAS,UAAU,SAAU,QAAQ;AAC/C,MAAI,aAAa,IAAI,eAAO,KAAK,OAAO,MAAM;AAC9C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACN/C,sBAAc,SAAS,WAAW,SAAU,QAAQ;AAChD,MAAI,aAAa,IAAI,gBAAQ,KAAK,OAAO,MAAM;AAC/C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,yBAAyB,eAAO;;;ACNjD,sBAAc,SAAS,eAAe,SAAU,QAAQ;AACpD,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,MAAM;AACnD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,sBAAsB,SAAU,QAAQ;AAC3D,MAAI,aAAa,IAAI,2BAAmB,KAAK,OAAO,MAAM;AAC1D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,oCAAoC,0BAAkB;;;ACNvE,sBAAc,SAAS,UAAU,SAAU,QAAQ;AAC/C,MAAI,aAAa,IAAI,eAAO,KAAK,OAAO,MAAM;AAC9C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACN/C,sBAAc,SAAS,iBAAiB,SAAU,QAAQ,UAAU;AAChE,MAAI,aAAa,IAAI,sBAAc,KAAK,OAAO,QAAQ,QAAQ;AAC/D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,+BAA+B,qBAAa;;;ACN7D,sBAAc,SAAS,uBAAuB,SAAU,QAAQ;AAC5D,MAAI,aAAa,IAAI,4BAAoB,KAAK,OAAO,MAAM;AAC3D,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,qCAAqC,2BAAmB;;;ACNzE,sBAAc,SAAS,mBAAmB,SAAU,QAAQ,UAAU;AAClE,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,QAAQ,QAAQ;AACjE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,aAAa,SAAU,QAAQ;AAClD,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,MAAM;AACjD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,QAAQ,SAAU,QAAQ;AAC7C,MAAI,aAAa,IAAI,aAAK,KAAK,OAAO,MAAM;AAC5C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,sBAAsB,YAAI;;;ACN3C,sBAAc,SAAS,UAAU,SAAU,QAAQ;AAC/C,MAAI,aAAa,IAAI,eAAO,KAAK,OAAO,MAAM;AAC9C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACN/C,sBAAc,SAAS,aAAa,SAAU,QAAQ;AAClD,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,MAAM;AACjD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,QAAQ,SAAU,QAAQ;AAC7C,MAAI,aAAa,IAAI,aAAK,KAAK,OAAO,MAAM;AAC5C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,sBAAsB,YAAI;;;ACN3C,sBAAc,SAAS,gBAAgB,SAAU,QAAQ;AACrD,MAAI,aAAa,IAAI,qBAAa,KAAK,OAAO,MAAM;AACpD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACN3D,sBAAc,SAAS,sBAAsB,SAAU,QAAQ,UAAU;AACrE,MAAI,aAAa,IAAI,2BAAmB,KAAK,OAAO,QAAQ,QAAQ;AACpE,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,oCAAoC,0BAAkB;;;ACNvE,sBAAc,SAAS,WAAW,SAAU,QAAQ;AAChD,MAAI,aAAa,IAAI,gBAAQ,KAAK,OAAO,MAAM;AAC/C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,yBAAyB,eAAO;;;ACNjD,sBAAc,SAAS,iBAAiB,SAAU,QAAQ;AACtD,MAAI,aAAa,IAAI,sBAAc,KAAK,OAAO,MAAM;AACrD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,+BAA+B,qBAAa;;;ACN7D,sBAAc,SAAS,aAAa,SAAU,QAAQ;AAClD,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,MAAM;AACjD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,aAAa,SAAU,QAAQ;AAClD,MAAI,aAAa,IAAI,kBAAU,KAAK,OAAO,MAAM;AACjD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,2BAA2B,iBAAS;;;ACNrD,sBAAc,SAAS,cAAc,SAAU,QAAQ;AACnD,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,MAAM;AAClD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,MAAM;AAC7C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,YAAY,SAAU,QAAQ;AACjD,MAAI,aAAa,IAAI,iBAAS,KAAK,OAAO,MAAM;AAChD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACNnD,sBAAc,SAAS,UAAU,SAAU,QAAQ;AAC/C,MAAI,aAAa,IAAI,eAAO,KAAK,OAAO,MAAM;AAC9C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACN/C,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,MAAM;AAC7C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,YAAY,SAAU,QAAQ;AACjD,MAAI,aAAa,IAAI,iBAAS,KAAK,OAAO,MAAM;AAChD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACNnD,sBAAc,SAAS,iBAAiB,SAAU,QAAQ;AACtD,MAAI,aAAa,IAAI,sBAAc,KAAK,OAAO,MAAM;AACrD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,+BAA+B,qBAAa;;;ACN7D,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,MAAM;AAC7C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,cAAc,SAAU,QAAQ;AACnD,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,MAAM;AAClD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,cAAc,SAAU,QAAQ;AACnD,MAAI,aAAa,IAAI,mBAAW,KAAK,OAAO,MAAM;AAClD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACNvD,sBAAc,SAAS,kBAAkB,SAAU,QAAQ;AACvD,MAAI,aAAa,IAAI,uBAAe,KAAK,OAAO,MAAM;AACtD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACN/D,sBAAc,SAAS,eAAe,SAAU,QAAQ;AACpD,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,MAAM;AACnD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,mBAAmB,SAAU,QAAQ;AACxD,MAAI,aAAa,IAAI,wBAAgB,KAAK,OAAO,MAAM;AACvD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,iCAAiC,uBAAe;;;ACNjE,sBAAc,SAAS,eAAe,SAAU,QAAQ;AACpD,MAAI,aAAa,IAAI,oBAAY,KAAK,OAAO,MAAM;AACnD,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACNzD,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,MAAI,aAAa,IAAI,cAAM,KAAK,OAAO,MAAM;AAC7C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACN7C,sBAAc,SAAS,WAAW,SAAU,QAAQ;AAChD,MAAI,aAAa,IAAI,gBAAQ,KAAK,OAAO,MAAM;AAC/C,OAAK,MAAM,IAAI,SAAS,UAAU;AAClC,SAAO;AACX,CAAC;AAED,iBAAS,QAAQ,yBAAyB,eAAO;;;ACNjD,sBAAc,SAAS,SAAS,SAAU,YAAY,QAAQ;AAC1D,SAAO,IAAI,cAAM,YAAY,MAAM;AACvC,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACJ7C,sBAAc,SAAS,gBAAgB,SAAU,YAAY,QAAQ;AACjE,SAAO,IAAI,qBAAa,YAAY,MAAM;AAC9C,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACJ3D,sBAAc,SAAS,cAAc,SAAU,YAAY,QAAQ;AAC/D,SAAO,IAAI,mBAAW,YAAY,MAAM;AAC5C,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACHvD,sBAAc,SAAS,OAAO,SAAU,YAAY,QAAQ;AACxD,MAAI,CAAC,qBAAa,UAAU,GAAG;AAC3B,aAAS;AACT,iBAAa,KAAK;AAAA,EACtB;AACA,SAAO,IAAI,YAAI,YAAY,MAAM;AACrC,CAAC;AAED,iBAAS,QAAQ,qBAAqB,WAAG;;;ACRzC,sBAAc,SAAS,SAAS,SAAU,YAAY,QAAQ;AAC1D,MAAI,CAAC,qBAAa,UAAU,GAAG;AAC3B,aAAS;AACT,iBAAa,KAAK;AAAA,EACtB;AACA,SAAO,IAAI,cAAM,YAAY,MAAM;AACvC,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACR7C,sBAAc,SAAS,SAAS,SAAU,YAAY,QAAQ;AAC1D,MAAI,CAAC,qBAAa,UAAU,GAAG;AAC3B,aAAS;AACT,iBAAa,KAAK;AAAA,EACtB;AACA,SAAO,IAAI,cAAM,YAAY,MAAM;AACvC,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACR7C,sBAAc,SAAS,OAAO,SAAU,YAAY,QAAQ;AACxD,MAAI,CAAC,qBAAa,UAAU,GAAG;AAC3B,aAAS;AACT,iBAAa,KAAK;AAAA,EACtB;AACA,SAAO,IAAI,YAAI,YAAY,MAAM;AACrC,CAAC;AAED,iBAAS,QAAQ,qBAAqB,WAAG;;;ACTzC,sBAAc,SAAS,QAAQ,SAAU,YAAY,QAAQ;AACzD,SAAO,IAAI,aAAK,YAAY,MAAM;AACtC,CAAC;AAED,iBAAS,QAAQ,sBAAsB,YAAI;;;ACJ3C,sBAAc,SAAS,SAAS,SAAU,QAAQ;AAC9C,SAAO,IAAI,cAAM,KAAK,OAAO,MAAM;AACvC,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACJ7C,sBAAc,SAAS,UAAU,SAAU,QAAQ;AAC/C,SAAO,IAAI,eAAO,KAAK,OAAO,MAAM;AACxC,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACJ/C,sBAAc,SAAS,QAAQ,SAAU,YAAY,QAAQ;AACzD,SAAO,IAAI,aAAK,YAAY,MAAM;AACtC,CAAC;AAED,iBAAS,QAAQ,sBAAsB,YAAI;;;ACJ3C,sBAAc,SAAS,SAAS,SAAU,YAAY,QAAQ;AAC1D,SAAO,IAAI,cAAM,YAAY,MAAM;AACvC,CAAC;AAED,iBAAS,QAAQ,uBAAuB,aAAK;;;ACJ7C,sBAAc,SAAS,kBAAkB,SAAU,YAAY,QAAQ;AACnE,SAAO,IAAI,uBAAe,YAAY,MAAM;AAChD,CAAC;AAED,iBAAS,QAAQ,gCAAgC,sBAAc;;;ACJ/D,sBAAc,SAAS,eAAe,SAAU,YAAY,QAAQ;AAChE,SAAO,IAAI,oBAAY,YAAY,MAAM;AAC7C,CAAC;AAED,iBAAS,QAAQ,6BAA6B,mBAAW;;;ACJzD,sBAAc,SAAS,QAAQ,SAAU,YAAY,QAAQ;AACzD,SAAO,IAAI,aAAK,YAAY,MAAM;AACtC,CAAC;AAED,iBAAS,QAAQ,sBAAsB,YAAI;;;ACJ3C,sBAAc,SAAS,UAAU,SAAU,YAAY,QAAQ;AAC3D,SAAO,IAAI,eAAO,YAAY,MAAM;AACxC,CAAC;AAED,iBAAS,QAAQ,wBAAwB,cAAM;;;ACJ/C,sBAAc,SAAS,cAAc,SAAU,YAAY,QAAQ;AAC/D,SAAO,IAAI,mBAAW,YAAY,MAAM;AAC5C,CAAC;AAED,iBAAS,QAAQ,4BAA4B,kBAAU;;;ACJvD,sBAAc,SAAS,YAAY,SAAU,YAAY,QAAQ;AAC7D,SAAO,IAAI,iBAAS,YAAY,MAAM;AAC1C,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACJnD,sBAAc,SAAS,YAAY,SAAU,YAAY,QAAQ;AAC7D,SAAO,IAAI,iBAAS,YAAY,MAAM;AAC1C,CAAC;AAED,iBAAS,QAAQ,0BAA0B,gBAAQ;;;ACJnD,sBAAc,SAAS,gBAAgB,SAAU,QAAQ;AACrD,SAAO,IAAI,qBAAa,KAAK,OAAO,MAAM;AAC9C,CAAC;AAED,iBAAS,QAAQ,8BAA8B,oBAAY;;;ACmI3D,IAAM,WAAN,cAAuB,OAAO,QAAQ,YAAY;AAAA,EAC9C,YAAY,OAAO,eAAe;AAC9B,UAAM,OAAO,aAAa;AAE1B,SAAK,MAAM,IAAI,sBAAc,KAAK;AAAA,EACtC;AAAA,EAEA,OAAO;AACH,QAAI,eAAe,KAAK,MAAM;AAC9B,iBAAa,GAAG,WAAW,KAAK,SAAS,IAAI;AAAA,EACjD;AAAA,EAEA,UAAU;AACN,SAAK,IAAI,QAAQ;AACjB,UAAM,QAAQ;AAAA,EAClB;AAAA,EAEA,aAAa,YAAY,SAAS,SAAS,UAAU;AACjD,QAAI,CAAC,WAAW,SAAS;AACrB,aAAO;AAAA,IACX;AACA,WAAO,0BAAkB,YAAY,SAAS,SAAS,QAAQ;AAAA,EACnE;AAAA,EAEA,IAAI,WAAW;AACX,WAAO,oBAAY,KAAK,OAAO,KAAK,MAAM,QAAQ,MAAM,IAAI;AAAA,EAChE;AAEJ;AAEA,IAAI,UAAU;AAAA,EACV,gBAAgB;AAAA,EAChB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,MAAM;AAAA,EACN,MAAM;AAAA,EACN,SAAS;AAAA,EACT,eAAe;AAAA,EACf,MAAM;AAAA,EACN,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA;AAAA,EAChB,uBAAuB;AAAA,EACvB,WAAW;AAAA,EACX,cAAc;AAAA,EACd,cAAc;AAAA,EACd,wBAAwB;AAAA,EACxB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,YAAY;AAAA,EACZ,cAAc;AAAA,EACd,OAAO;AAAA,EACP,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,aAAa;AAAA,EACb,iBAAiB;AACrB;AAEA,OAAO;AAAA,EACH,SAAS;AAAA,EACT;AACJ;AAGA,IAAO,oBAAQ;", "names": ["NinePatch_default"]}